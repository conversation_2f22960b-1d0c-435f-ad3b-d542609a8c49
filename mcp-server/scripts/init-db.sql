-- OneFoodDialer MCP Server Database Initialization
-- This script creates the necessary databases for the MCP server and services

-- Create MCP server database
CREATE DATABASE IF NOT EXISTS `mcp_server` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create service databases
CREATE DATABASE IF NOT EXISTS `auth_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `customer_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `payment_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `quickserve_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `admin_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `analytics_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `catalogue_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `kitchen_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `delivery_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `meal_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `subscription_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `notification_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `invoice_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `misscall_service_v12` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create service users with appropriate permissions
CREATE USER IF NOT EXISTS 'auth_user'@'%' IDENTIFIED BY 'auth_password';
CREATE USER IF NOT EXISTS 'customer_user'@'%' IDENTIFIED BY 'customer_password';
CREATE USER IF NOT EXISTS 'payment_user'@'%' IDENTIFIED BY 'payment_password';
CREATE USER IF NOT EXISTS 'quickserve_user'@'%' IDENTIFIED BY 'quickserve_password';
CREATE USER IF NOT EXISTS 'admin_user'@'%' IDENTIFIED BY 'admin_password';
CREATE USER IF NOT EXISTS 'analytics_user'@'%' IDENTIFIED BY 'analytics_password';
CREATE USER IF NOT EXISTS 'catalogue_user'@'%' IDENTIFIED BY 'catalogue_password';
CREATE USER IF NOT EXISTS 'kitchen_user'@'%' IDENTIFIED BY 'kitchen_password';
CREATE USER IF NOT EXISTS 'delivery_user'@'%' IDENTIFIED BY 'delivery_password';
CREATE USER IF NOT EXISTS 'meal_user'@'%' IDENTIFIED BY 'meal_password';
CREATE USER IF NOT EXISTS 'subscription_user'@'%' IDENTIFIED BY 'subscription_password';
CREATE USER IF NOT EXISTS 'notification_user'@'%' IDENTIFIED BY 'notification_password';
CREATE USER IF NOT EXISTS 'invoice_user'@'%' IDENTIFIED BY 'invoice_password';
CREATE USER IF NOT EXISTS 'misscall_user'@'%' IDENTIFIED BY 'misscall_password';

-- Grant permissions
GRANT ALL PRIVILEGES ON `auth_service_v12`.* TO 'auth_user'@'%';
GRANT ALL PRIVILEGES ON `customer_service_v12`.* TO 'customer_user'@'%';
GRANT ALL PRIVILEGES ON `payment_service_v12`.* TO 'payment_user'@'%';
GRANT ALL PRIVILEGES ON `quickserve_service_v12`.* TO 'quickserve_user'@'%';
GRANT ALL PRIVILEGES ON `admin_service_v12`.* TO 'admin_user'@'%';
GRANT ALL PRIVILEGES ON `analytics_service_v12`.* TO 'analytics_user'@'%';
GRANT ALL PRIVILEGES ON `catalogue_service_v12`.* TO 'catalogue_user'@'%';
GRANT ALL PRIVILEGES ON `kitchen_service_v12`.* TO 'kitchen_user'@'%';
GRANT ALL PRIVILEGES ON `delivery_service_v12`.* TO 'delivery_user'@'%';
GRANT ALL PRIVILEGES ON `meal_service_v12`.* TO 'meal_user'@'%';
GRANT ALL PRIVILEGES ON `subscription_service_v12`.* TO 'subscription_user'@'%';
GRANT ALL PRIVILEGES ON `notification_service_v12`.* TO 'notification_user'@'%';
GRANT ALL PRIVILEGES ON `invoice_service_v12`.* TO 'invoice_user'@'%';
GRANT ALL PRIVILEGES ON `misscall_service_v12`.* TO 'misscall_user'@'%';

-- Grant MCP server access to all service databases for monitoring and management
GRANT SELECT, INSERT, UPDATE, DELETE ON *.* TO 'root'@'%';

-- Create MCP server tables
USE `mcp_server`;

-- Health check logs table
CREATE TABLE IF NOT EXISTS `health_checks` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `service_name` VARCHAR(100) NOT NULL,
    `status` ENUM('healthy', 'unhealthy', 'degraded', 'unknown') NOT NULL,
    `response_time` INT UNSIGNED NOT NULL,
    `checks` JSON,
    `error_message` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
);

-- Service metrics table
CREATE TABLE IF NOT EXISTS `service_metrics` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `service_name` VARCHAR(100) NOT NULL,
    `requests_per_minute` DECIMAL(10,2) DEFAULT 0,
    `average_response_time` DECIMAL(10,2) DEFAULT 0,
    `error_rate` DECIMAL(5,2) DEFAULT 0,
    `cpu_usage` DECIMAL(5,2) NULL,
    `memory_usage` DECIMAL(5,2) NULL,
    `active_connections` INT UNSIGNED NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_created_at` (`created_at`)
);

-- Test results table
CREATE TABLE IF NOT EXISTS `test_results` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `test_name` VARCHAR(200) NOT NULL,
    `service_name` VARCHAR(100) NOT NULL,
    `test_type` ENUM('api', 'database', 'integration') NOT NULL,
    `success` BOOLEAN NOT NULL,
    `duration` INT UNSIGNED NOT NULL,
    `result_data` JSON NULL,
    `error_message` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_test_type` (`test_type`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
);

-- API call logs table
CREATE TABLE IF NOT EXISTS `api_call_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `service_name` VARCHAR(100) NOT NULL,
    `endpoint` VARCHAR(500) NOT NULL,
    `method` VARCHAR(10) NOT NULL,
    `status_code` INT UNSIGNED NOT NULL,
    `response_time` INT UNSIGNED NOT NULL,
    `success` BOOLEAN NOT NULL,
    `error_message` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_endpoint` (`endpoint`(100)),
    INDEX `idx_method` (`method`),
    INDEX `idx_status_code` (`status_code`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
);

-- Database query logs table
CREATE TABLE IF NOT EXISTS `database_query_logs` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `service_name` VARCHAR(100) NOT NULL,
    `query_hash` VARCHAR(64) NOT NULL,
    `execution_time` INT UNSIGNED NOT NULL,
    `affected_rows` INT UNSIGNED DEFAULT 0,
    `success` BOOLEAN NOT NULL,
    `error_message` TEXT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_query_hash` (`query_hash`),
    INDEX `idx_execution_time` (`execution_time`),
    INDEX `idx_success` (`success`),
    INDEX `idx_created_at` (`created_at`)
);

-- Service configurations table
CREATE TABLE IF NOT EXISTS `service_configurations` (
    `id` BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `service_name` VARCHAR(100) NOT NULL UNIQUE,
    `config_data` JSON NOT NULL,
    `version` VARCHAR(20) DEFAULT '1.0.0',
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX `idx_service_name` (`service_name`),
    INDEX `idx_is_active` (`is_active`)
);

-- Insert default service configurations
INSERT INTO `service_configurations` (`service_name`, `config_data`, `version`) VALUES
('auth-service-v12', '{"url": "http://auth-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('customer-service-v12', '{"url": "http://customer-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('payment-service-v12', '{"url": "http://payment-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('quickserve-service-v12', '{"url": "http://quickserve-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('admin-service-v12', '{"url": "http://admin-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('analytics-service-v12', '{"url": "http://analytics-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('catalogue-service-v12', '{"url": "http://catalogue-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('kitchen-service-v12', '{"url": "http://kitchen-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('delivery-service-v12', '{"url": "http://delivery-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('meal-service-v12', '{"url": "http://meal-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('subscription-service-v12', '{"url": "http://subscription-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('notification-service-v12', '{"url": "http://notification-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('invoice-service-v12', '{"url": "http://invoice-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0'),
('misscall-service-v12', '{"url": "http://misscall-service-v12:8000/api", "timeout": 30, "retries": 3}', '12.0')
ON DUPLICATE KEY UPDATE 
    `config_data` = VALUES(`config_data`),
    `version` = VALUES(`version`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Flush privileges
FLUSH PRIVILEGES;

-- Show created databases
SHOW DATABASES;
