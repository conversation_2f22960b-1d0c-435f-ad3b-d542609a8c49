#!/bin/bash

# OneFoodDialer MCP Server Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="onefooddialer-mcp"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi

    # Check if services directory exists
    if [ ! -d "../../services" ]; then
        log_error "Services directory not found. Please ensure you're running this from the mcp-server directory."
        exit 1
    fi
    
    log_success "Requirements check passed"
}

setup_environment() {
    log_info "Setting up environment..."
    
    # Create .env file if it doesn't exist
    # if [ ! -f "$ENV_FILE" ]; then
    #     log_info "Creating .env file from template..."
    #     cp .env.example "$ENV_FILE"
    #     log_warning "Please edit .env file with your configuration before continuing"
    #     read -p "Press Enter to continue after editing .env file..."
    # fi
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p config
    mkdir -p scripts
    mkdir -p monitoring/grafana/dashboards
    mkdir -p monitoring/grafana/datasources
    
    log_success "Environment setup completed"
}

build_and_deploy() {
    log_info "Building and deploying MCP server..."
    
    # Build TypeScript
    log_info "Building TypeScript..."
    npm run build
    
    # Stop existing containers
    log_info "Stopping existing containers..."
    docker-compose down --remove-orphans || true
    
    # Build and start containers
    log_info "Building and starting containers..."
    docker-compose up --build -d
    
    # Wait for services to be ready
    log_info "Waiting for services to be ready..."
    sleep 30
    
    # Check service health
    check_health
    
    log_success "Deployment completed successfully"
}

check_health() {
    log_info "Checking service health..."
    
    # Check MCP server
    if docker-compose ps mcp-server | grep -q "Up"; then
        log_success "MCP Server is running"
    else
        log_error "MCP Server failed to start"
        docker-compose logs mcp-server
        exit 1
    fi
    
    # Check MySQL
    if docker-compose ps mysql | grep -q "Up"; then
        log_success "MySQL is running"
    else
        log_error "MySQL failed to start"
        docker-compose logs mysql
        exit 1
    fi
    
    # Check Redis
    if docker-compose ps redis | grep -q "Up"; then
        log_success "Redis is running"
    else
        log_error "Redis failed to start"
        docker-compose logs redis
        exit 1
    fi
}

show_status() {
    log_info "Service Status:"
    docker-compose ps
    
    echo ""
    log_info "Service URLs:"
    echo "  MCP Server: Available via stdio transport"
    echo "  MySQL: localhost:3306"
    echo "  Redis: localhost:6379"
    
    if docker-compose --profile monitoring ps | grep -q prometheus; then
        echo "  Prometheus: http://localhost:9090"
        echo "  Grafana: http://localhost:3001 (admin/admin)"
    fi
}

show_logs() {
    log_info "Showing recent logs..."
    docker-compose logs --tail=50 -f
}

cleanup() {
    log_info "Cleaning up..."
    docker-compose down --volumes --remove-orphans
    docker system prune -f
    log_success "Cleanup completed"
}

# Main script
case "${1:-deploy}" in
    "deploy")
        log_info "Starting MCP Server deployment..."
        check_requirements
        setup_environment
        build_and_deploy
        show_status
        ;;
    "start")
        log_info "Starting MCP Server..."
        docker-compose up -d
        check_health
        show_status
        ;;
    "stop")
        log_info "Stopping MCP Server..."
        docker-compose down
        log_success "MCP Server stopped"
        ;;
    "restart")
        log_info "Restarting MCP Server..."
        docker-compose restart
        check_health
        show_status
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "health")
        check_health
        ;;
    "cleanup")
        cleanup
        ;;
    "monitoring")
        log_info "Starting with monitoring stack..."
        docker-compose --profile monitoring up -d
        check_health
        show_status
        ;;
    "update")
        log_info "Updating MCP Server..."
        git pull
        npm install
        npm run build
        docker-compose up --build -d
        check_health
        show_status
        ;;
    *)
        echo "Usage: $0 {deploy|start|stop|restart|status|logs|health|cleanup|monitoring|update}"
        echo ""
        echo "Commands:"
        echo "  deploy     - Full deployment (default)"
        echo "  start      - Start services"
        echo "  stop       - Stop services"
        echo "  restart    - Restart services"
        echo "  status     - Show service status"
        echo "  logs       - Show service logs"
        echo "  health     - Check service health"
        echo "  cleanup    - Stop and remove all containers and volumes"
        echo "  monitoring - Start with monitoring stack (Prometheus + Grafana)"
        echo "  update     - Update and redeploy"
        exit 1
        ;;
esac
