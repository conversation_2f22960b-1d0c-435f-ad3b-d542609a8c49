{"mcpServers": {"onefooddialer-services": {"command": "node", "args": ["dist/index.js"], "cwd": "./mcp-server", "env": {"NODE_ENV": "production", "LOG_LEVEL": "info"}}}, "tools": [{"name": "list_services", "description": "List all OneFoodDialer microservices and their status", "server": "onefooddialer-services"}, {"name": "get_service_health", "description": "Check health status of a specific OneFoodDialer service", "server": "onefooddialer-services"}, {"name": "test_api_endpoint", "description": "Test any API endpoint in the OneFoodDialer ecosystem", "server": "onefooddialer-services"}, {"name": "query_database", "description": "Execute SQL queries on OneFoodDialer service databases", "server": "onefooddialer-services"}, {"name": "get_service_openapi", "description": "Get OpenAPI specification for OneFoodDialer services", "server": "onefooddialer-services"}, {"name": "get_service_config", "description": "Get configuration for OneFoodDialer services", "server": "onefooddialer-services"}, {"name": "get_service_metrics", "description": "Get performance metrics for OneFoodDialer services", "server": "onefooddialer-services"}, {"name": "test_service_integration", "description": "Test integration between OneFoodDialer services", "server": "onefooddialer-services"}], "resources": [{"name": "service_documentation", "description": "OneFoodDialer service documentation and API specs", "server": "onefooddialer-services"}, {"name": "health_reports", "description": "Real-time health monitoring reports", "server": "onefooddialer-services"}, {"name": "test_results", "description": "Automated test execution results", "server": "onefooddialer-services"}]}