# OneFoodDialer MCP Server Configuration

# Logging
LOG_LEVEL=info

# Services Base Configuration
SERVICES_PATH=../services

# Default Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=

# Default Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_TTL=3600

# Service URLs (Override defaults)
AUTH_SERVICE_URL=http://localhost:8001/api
CUSTOMER_SERVICE_URL=http://localhost:8002/api
PAYMENT_SERVICE_URL=http://localhost:8003/api
QUICKSERVE_SERVICE_URL=http://localhost:8004/api
ADMIN_SERVICE_URL=http://localhost:8005/api
ANALYTICS_SERVICE_URL=http://localhost:8006/api
CATALOGUE_SERVICE_URL=http://localhost:8007/api
KITCHEN_SERVICE_URL=http://localhost:8008/api
DELIVERY_SERVICE_URL=http://localhost:8009/api
MEAL_SERVICE_URL=http://localhost:8010/api
SUBSCRIPTION_SERVICE_URL=http://localhost:8011/api
NOTIFICATION_SERVICE_URL=http://localhost:8012/api
INVOICE_SERVICE_URL=http://localhost:8013/api
MISSCALL_SERVICE_URL=http://localhost:8014/api

# Service Timeouts (seconds)
AUTH_SERVICE_TIMEOUT=30
CUSTOMER_SERVICE_TIMEOUT=30
PAYMENT_SERVICE_TIMEOUT=30
QUICKSERVE_SERVICE_TIMEOUT=30
ADMIN_SERVICE_TIMEOUT=30
ANALYTICS_SERVICE_TIMEOUT=30
CATALOGUE_SERVICE_TIMEOUT=30
KITCHEN_SERVICE_TIMEOUT=30
DELIVERY_SERVICE_TIMEOUT=30
MEAL_SERVICE_TIMEOUT=30
SUBSCRIPTION_SERVICE_TIMEOUT=30
NOTIFICATION_SERVICE_TIMEOUT=30
INVOICE_SERVICE_TIMEOUT=30
MISSCALL_SERVICE_TIMEOUT=30

# Service Retry Configuration
AUTH_SERVICE_RETRIES=3
CUSTOMER_SERVICE_RETRIES=3
PAYMENT_SERVICE_RETRIES=3
QUICKSERVE_SERVICE_RETRIES=3
ADMIN_SERVICE_RETRIES=3
ANALYTICS_SERVICE_RETRIES=3
CATALOGUE_SERVICE_RETRIES=3
KITCHEN_SERVICE_RETRIES=3
DELIVERY_SERVICE_RETRIES=3
MEAL_SERVICE_RETRIES=3
SUBSCRIPTION_SERVICE_RETRIES=3
NOTIFICATION_SERVICE_RETRIES=3
INVOICE_SERVICE_RETRIES=3
MISSCALL_SERVICE_RETRIES=3

# Kong Gateway Configuration
KONG_ADMIN_URL=http://localhost:8001
KONG_PROXY_URL=http://localhost:8000

# Monitoring Configuration
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Development Configuration
NODE_ENV=development
DEBUG=onefooddialer:*
