global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # MCP Server metrics (if we add a metrics endpoint)
  - job_name: 'mcp-server'
    static_configs:
      - targets: ['mcp-server:3000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # OneFoodDialer Services
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'customer-service'
    static_configs:
      - targets: ['customer-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'payment-service'
    static_configs:
      - targets: ['payment-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'quickserve-service'
    static_configs:
      - targets: ['quickserve-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'admin-service'
    static_configs:
      - targets: ['admin-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'catalogue-service'
    static_configs:
      - targets: ['catalogue-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'kitchen-service'
    static_configs:
      - targets: ['kitchen-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'delivery-service'
    static_configs:
      - targets: ['delivery-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'meal-service'
    static_configs:
      - targets: ['meal-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'subscription-service'
    static_configs:
      - targets: ['subscription-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'notification-service'
    static_configs:
      - targets: ['notification-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'invoice-service'
    static_configs:
      - targets: ['invoice-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'misscall-service'
    static_configs:
      - targets: ['misscall-service-v12:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Infrastructure services
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    metrics_path: '/metrics'
    scrape_interval: 60s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 60s

  # Kong Gateway (if available)
  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: '/metrics'
    scrape_interval: 30s
