{"name": "onefooddialer-services-mcp", "version": "1.0.0", "description": "MCP Server for OneFoodDialer 2025 Services Architecture", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "cli": "node dist/cli/mcp-cli.js", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "setup": "npm install && npm run build && cp .env.example .env"}, "bin": {"mcp-cli": "./dist/cli/mcp-cli.js"}, "keywords": ["mcp", "microservices", "laravel", "api", "onefooddialer"], "author": "OneFoodDialer Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "axios": "^1.6.0", "yaml": "^2.3.4", "mysql2": "^3.6.5", "redis": "^4.6.0", "winston": "^3.11.0", "dotenv": "^16.3.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "commander": "^11.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}