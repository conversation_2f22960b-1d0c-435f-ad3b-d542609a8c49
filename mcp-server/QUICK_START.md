# OneFoodDialer MCP Server - Quick Start Guide

This guide will help you get the OneFoodDialer MCP Server up and running quickly.

## Prerequisites

- Node.js 18+ installed
- Docker and Docker Compose installed
- Access to the OneFoodDialer services directory

## Quick Setup (5 minutes)

### 1. Install and Setup

```bash
# Navigate to the MCP server directory
cd mcp-server

# Quick setup (installs dependencies, builds, and creates .env)
npm run setup

# Edit the .env file with your configuration
nano .env
```

### 2. Deploy with Docker

```bash
# Deploy everything (builds and starts all services)
npm run deploy

# Or use the script directly
./scripts/deploy.sh
```

### 3. Verify Installation

```bash
# Check service status
./scripts/deploy.sh status

# Test the CLI
npm run cli list-services

# Check health of all services
npm run cli health --all
```

## Manual Setup (if you prefer step-by-step)

### 1. Install Dependencies

```bash
npm install
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

Key settings to configure:
- Database credentials
- Service URLs (if different from defaults)
- JWT secret
- Redis configuration

### 3. Build the Project

```bash
npm run build
```

### 4. Start Services

```bash
# Start with Docker Compose
docker-compose up -d

# Or start with monitoring stack
docker-compose --profile monitoring up -d
```

## Using the MCP Server

### CLI Commands

```bash
# List all services
npm run cli list-services

# Check service health
npm run cli health --service auth-service-v12

# Test an API endpoint
npm run cli test-api \
  --service customer-service-v12 \
  --endpoint /customers \
  --method GET

# Query a database
npm run cli query-db \
  --service customer-service-v12 \
  --query "SELECT COUNT(*) FROM customers"

# Run health tests
npm run cli run-tests --type health

# Get service metrics
npm run cli metrics --service quickserve-service-v12
```

### MCP Client Integration

Add to your MCP client configuration:

```json
{
  "mcpServers": {
    "onefooddialer-services": {
      "command": "node",
      "args": ["dist/index.js"],
      "cwd": "./mcp-server"
    }
  }
}
```

### Available Tools

- `list_services` - List all microservices
- `get_service_health` - Check service health
- `test_api_endpoint` - Test API endpoints
- `query_database` - Execute database queries
- `get_service_openapi` - Get API specifications
- `get_service_config` - Get service configuration
- `get_service_metrics` - Get performance metrics
- `test_service_integration` - Test service integrations

## Common Use Cases

### 1. Service Health Monitoring

```bash
# Check all services
npm run cli health --all

# Start continuous monitoring
npm run cli monitor --interval 5
```

### 2. API Testing

```bash
# Test customer registration
npm run cli test-api \
  --service customer-service-v12 \
  --endpoint /customers \
  --method POST \
  --data '{"name":"John Doe","email":"<EMAIL>"}'

# Test order creation
npm run cli test-api \
  --service quickserve-service-v12 \
  --endpoint /orders \
  --method POST \
  --data '{"customer_id":1,"items":[{"product_id":1,"quantity":2}]}'
```

### 3. Database Operations

```bash
# List tables in a service
npm run cli list-tables --service customer-service-v12

# Describe table structure
npm run cli describe-table \
  --service customer-service-v12 \
  --table customers

# Backup database
npm run cli backup-db \
  --service customer-service-v12 \
  --output customer-backup.sql
```

### 4. Integration Testing

```bash
# Test auth integration
npm run cli run-tests --type integration

# Generate service-specific tests
npm run cli run-tests --service auth-service-v12
```

## Monitoring and Observability

### Access Monitoring Dashboards

If you started with the monitoring profile:

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001 (admin/admin)

### View Logs

```bash
# View all service logs
./scripts/deploy.sh logs

# View specific service logs
docker-compose logs -f mcp-server
```

## Troubleshooting

### Common Issues

1. **Services not found**
   ```bash
   # Ensure services directory is accessible
   ls -la ../services
   ```

2. **Database connection errors**
   ```bash
   # Check database status
   docker-compose ps mysql
   
   # View database logs
   docker-compose logs mysql
   ```

3. **Permission errors**
   ```bash
   # Make scripts executable
   chmod +x scripts/*.sh
   ```

### Reset Everything

```bash
# Stop and remove all containers and volumes
./scripts/deploy.sh cleanup

# Redeploy from scratch
./scripts/deploy.sh deploy
```

## Development Mode

```bash
# Start in development mode with hot reload
npm run dev

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

## Next Steps

1. **Integrate with your MCP client** using the provided configuration
2. **Set up monitoring** with Prometheus and Grafana
3. **Create custom test suites** for your specific use cases
4. **Extend the server** with additional tools and resources

## Support

- Check the main README.md for detailed documentation
- View service logs for troubleshooting
- Use the CLI help: `npm run cli --help`

## Quick Reference

```bash
# Essential commands
npm run setup           # Initial setup
npm run deploy          # Deploy everything
npm run cli --help      # CLI help
./scripts/deploy.sh status  # Check status
./scripts/deploy.sh logs    # View logs
```
