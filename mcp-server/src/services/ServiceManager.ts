import { ConfigManager } from '../config/ConfigManager.js';
import { Logger } from '../utils/Logger.js';
import axios from 'axios';
import * as fs from 'fs/promises';
import * as path from 'path';
import YAML from 'yaml';

export interface ServiceInfo {
  name: string;
  version: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  url: string;
  description?: string;
  endpoints?: string[];
  lastChecked?: Date;
  responseTime?: number;
}

export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  checks: {
    database?: boolean;
    redis?: boolean;
    external_apis?: boolean;
  };
  uptime?: number;
  version?: string;
  timestamp: Date;
}

export interface ServiceMetrics {
  service: string;
  requests_per_minute: number;
  average_response_time: number;
  error_rate: number;
  cpu_usage?: number;
  memory_usage?: number;
  active_connections?: number;
  timestamp: Date;
}

export class ServiceManager {
  private logger: Logger;
  private configManager: ConfigManager;
  private servicesPath: string;

  constructor(configManager: ConfigManager) {
    this.logger = new Logger('ServiceManager');
    this.configManager = configManager;
    this.servicesPath = path.join(process.cwd(), '../services');
  }

  async listServices(filter?: string): Promise<ServiceInfo[]> {
    try {
      const services: ServiceInfo[] = [];
      const serviceDirs = await fs.readdir(this.servicesPath);

      for (const dir of serviceDirs) {
        if (dir.endsWith('-v12') || dir.endsWith('-service')) {
          const servicePath = path.join(this.servicesPath, dir);
          const stat = await fs.stat(servicePath);
          
          if (stat.isDirectory()) {
            const serviceInfo = await this.getServiceInfo(dir, servicePath);
            if (!filter || serviceInfo.name.includes(filter) || serviceInfo.status === filter) {
              services.push(serviceInfo);
            }
          }
        }
      }

      return services.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      this.logger.error('Error listing services:', error);
      throw error;
    }
  }

  private async getServiceInfo(dirName: string, servicePath: string): Promise<ServiceInfo> {
    const serviceInfo: ServiceInfo = {
      name: dirName,
      version: 'unknown',
      status: 'unknown',
      url: '',
    };

    try {
      // Try to read composer.json for Laravel services
      const composerPath = path.join(servicePath, 'composer.json');
      try {
        const composerContent = await fs.readFile(composerPath, 'utf-8');
        const composer = JSON.parse(composerContent);
        serviceInfo.description = composer.description;
        serviceInfo.version = composer.version || '1.0.0';
      } catch {
        // Composer.json not found or invalid
      }

      // Try to read OpenAPI spec
      const openapiPath = path.join(servicePath, 'openapi.yaml');
      try {
        const openapiContent = await fs.readFile(openapiPath, 'utf-8');
        const openapi = YAML.parse(openapiContent);
        if (openapi.info) {
          serviceInfo.description = serviceInfo.description || openapi.info.description;
          serviceInfo.version = openapi.info.version || serviceInfo.version;
        }
        if (openapi.servers && openapi.servers.length > 0) {
          serviceInfo.url = openapi.servers[0].url;
        }
      } catch {
        // OpenAPI spec not found or invalid
      }

      // Set default URL based on service name
      if (!serviceInfo.url) {
        const serviceName = dirName.replace('-v12', '').replace('-service', '');
        serviceInfo.url = `http://${dirName}:8000/api`;
      }

      // Check service health
      serviceInfo.status = await this.checkServiceStatus(serviceInfo.url);

    } catch (error) {
      this.logger.error(`Error getting info for service ${dirName}:`, error);
    }

    return serviceInfo;
  }

  private async checkServiceStatus(url: string): Promise<'healthy' | 'unhealthy' | 'unknown'> {
    try {
      const healthUrl = `${url}/health`;
      const response = await axios.get(healthUrl, { timeout: 5000 });
      return response.status === 200 ? 'healthy' : 'unhealthy';
    } catch {
      return 'unhealthy';
    }
  }

  async getServiceHealth(serviceName: string): Promise<ServiceHealth> {
    try {
      const serviceUrl = await this.getServiceUrl(serviceName);
      const healthUrl = `${serviceUrl}/health`;
      
      const startTime = Date.now();
      const response = await axios.get(healthUrl, { timeout: 10000 });
      const responseTime = Date.now() - startTime;

      const healthData = response.data;
      
      return {
        service: serviceName,
        status: healthData.status === 'ok' ? 'healthy' : 'unhealthy',
        checks: {
          database: healthData.database !== false,
          redis: healthData.redis !== false,
          external_apis: healthData.external_apis !== false,
        },
        uptime: healthData.uptime,
        version: healthData.version,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error getting health for service ${serviceName}:`, error);
      return {
        service: serviceName,
        status: 'unhealthy',
        checks: {},
        timestamp: new Date(),
      };
    }
  }

  async getServiceOpenAPI(serviceName: string): Promise<any> {
    try {
      const servicePath = path.join(this.servicesPath, serviceName);
      const openapiPath = path.join(servicePath, 'openapi.yaml');
      
      const openapiContent = await fs.readFile(openapiPath, 'utf-8');
      return YAML.parse(openapiContent);
    } catch (error) {
      this.logger.error(`Error reading OpenAPI spec for ${serviceName}:`, error);
      throw new Error(`OpenAPI specification not found for service: ${serviceName}`);
    }
  }

  async getServiceMetrics(serviceName: string, timeRange: string = '1h'): Promise<ServiceMetrics> {
    try {
      const serviceUrl = await this.getServiceUrl(serviceName);
      const metricsUrl = `${serviceUrl}/metrics`;
      
      const response = await axios.get(metricsUrl, { 
        timeout: 10000,
        params: { range: timeRange }
      });

      const metricsData = response.data;
      
      return {
        service: serviceName,
        requests_per_minute: metricsData.requests_per_minute || 0,
        average_response_time: metricsData.average_response_time || 0,
        error_rate: metricsData.error_rate || 0,
        cpu_usage: metricsData.cpu_usage,
        memory_usage: metricsData.memory_usage,
        active_connections: metricsData.active_connections,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error getting metrics for service ${serviceName}:`, error);
      // Return default metrics if service doesn't support metrics endpoint
      return {
        service: serviceName,
        requests_per_minute: 0,
        average_response_time: 0,
        error_rate: 0,
        timestamp: new Date(),
      };
    }
  }

  async testServiceIntegration(
    sourceService: string,
    targetService: string,
    testType: string
  ): Promise<any> {
    try {
      const sourceUrl = await this.getServiceUrl(sourceService);
      const targetUrl = await this.getServiceUrl(targetService);

      switch (testType) {
        case 'auth':
          return await this.testAuthIntegration(sourceUrl, targetUrl);
        case 'api_call':
          return await this.testApiCallIntegration(sourceUrl, targetUrl);
        case 'database':
          return await this.testDatabaseIntegration(sourceService, targetService);
        case 'queue':
          return await this.testQueueIntegration(sourceService, targetService);
        default:
          throw new Error(`Unknown test type: ${testType}`);
      }
    } catch (error) {
      this.logger.error(`Error testing integration ${sourceService} -> ${targetService}:`, error);
      throw error;
    }
  }

  private async testAuthIntegration(sourceUrl: string, targetUrl: string): Promise<any> {
    // Test JWT token validation between services
    try {
      const authResponse = await axios.post(`${sourceUrl}/auth/login`, {
        email: '<EMAIL>',
        password: 'test123'
      });

      const token = authResponse.data.token;
      
      const testResponse = await axios.get(`${targetUrl}/protected-endpoint`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return {
        success: true,
        message: 'Auth integration test passed',
        details: {
          token_obtained: !!token,
          protected_endpoint_accessible: testResponse.status === 200
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Auth integration test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async testApiCallIntegration(sourceUrl: string, targetUrl: string): Promise<any> {
    // Test API call between services
    try {
      const response = await axios.get(`${sourceUrl}/test-integration`, {
        params: { target_service: targetUrl }
      });

      return {
        success: true,
        message: 'API call integration test passed',
        response: response.data
      };
    } catch (error) {
      return {
        success: false,
        message: 'API call integration test failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  private async testDatabaseIntegration(sourceService: string, targetService: string): Promise<any> {
    // Test database connectivity between services
    return {
      success: true,
      message: 'Database integration test - implementation needed',
      note: 'This would test shared database access patterns'
    };
  }

  private async testQueueIntegration(sourceService: string, targetService: string): Promise<any> {
    // Test queue/messaging integration between services
    return {
      success: true,
      message: 'Queue integration test - implementation needed',
      note: 'This would test Redis queue communication'
    };
  }

  private async getServiceUrl(serviceName: string): Promise<string> {
    const config = this.configManager.getServiceConfig(serviceName);
    return config.url || `http://${serviceName}:8000/api`;
  }
}
