#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import { ServiceManager } from './services/ServiceManager.js';
import { DatabaseManager } from './database/DatabaseManager.js';
import { ApiClient } from './api/ApiClient.js';
import { ConfigManager } from './config/ConfigManager.js';
import { Logger } from './utils/Logger.js';

class OneFoodDialerMCPServer {
  private server: Server;
  private serviceManager: ServiceManager;
  private databaseManager: DatabaseManager;
  private apiClient: ApiClient;
  private configManager: ConfigManager;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('OneFoodDialerMCP');
    this.configManager = new ConfigManager();
    this.serviceManager = new ServiceManager(this.configManager);
    this.databaseManager = new DatabaseManager(this.configManager);
    this.apiClient = new ApiClient(this.configManager);
    
    this.server = new Server(
      {
        name: 'onefooddialer-services-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          // Service Management Tools
          {
            name: 'list_services',
            description: 'List all available microservices and their status',
            inputSchema: {
              type: 'object',
              properties: {
                filter: {
                  type: 'string',
                  description: 'Filter services by name or status',
                },
              },
            },
          },
          {
            name: 'get_service_health',
            description: 'Check health status of a specific service',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Name of the service to check',
                },
              },
              required: ['serviceName'],
            },
          },
          {
            name: 'get_service_openapi',
            description: 'Get OpenAPI specification for a service',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Name of the service',
                },
              },
              required: ['serviceName'],
            },
          },
          // API Testing Tools
          {
            name: 'test_api_endpoint',
            description: 'Test an API endpoint with specified parameters',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Name of the service',
                },
                endpoint: {
                  type: 'string',
                  description: 'API endpoint path',
                },
                method: {
                  type: 'string',
                  enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
                  description: 'HTTP method',
                },
                data: {
                  type: 'object',
                  description: 'Request payload',
                },
                headers: {
                  type: 'object',
                  description: 'Request headers',
                },
              },
              required: ['serviceName', 'endpoint', 'method'],
            },
          },
          // Database Tools
          {
            name: 'query_database',
            description: 'Execute a database query',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Service database to query',
                },
                query: {
                  type: 'string',
                  description: 'SQL query to execute',
                },
                params: {
                  type: 'array',
                  description: 'Query parameters',
                },
              },
              required: ['serviceName', 'query'],
            },
          },
          // Configuration Tools
          {
            name: 'get_service_config',
            description: 'Get configuration for a service',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Name of the service',
                },
              },
              required: ['serviceName'],
            },
          },
          // Monitoring Tools
          {
            name: 'get_service_metrics',
            description: 'Get performance metrics for a service',
            inputSchema: {
              type: 'object',
              properties: {
                serviceName: {
                  type: 'string',
                  description: 'Name of the service',
                },
                timeRange: {
                  type: 'string',
                  description: 'Time range for metrics (1h, 24h, 7d)',
                  default: '1h',
                },
              },
              required: ['serviceName'],
            },
          },
          // Integration Tools
          {
            name: 'test_service_integration',
            description: 'Test integration between services',
            inputSchema: {
              type: 'object',
              properties: {
                sourceService: {
                  type: 'string',
                  description: 'Source service name',
                },
                targetService: {
                  type: 'string',
                  description: 'Target service name',
                },
                testType: {
                  type: 'string',
                  enum: ['auth', 'api_call', 'database', 'queue'],
                  description: 'Type of integration test',
                },
              },
              required: ['sourceService', 'targetService', 'testType'],
            },
          },
        ],
      };
    });

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'list_services':
            return await this.handleListServices(args);
          case 'get_service_health':
            return await this.handleGetServiceHealth(args);
          case 'get_service_openapi':
            return await this.handleGetServiceOpenAPI(args);
          case 'test_api_endpoint':
            return await this.handleTestApiEndpoint(args);
          case 'query_database':
            return await this.handleQueryDatabase(args);
          case 'get_service_config':
            return await this.handleGetServiceConfig(args);
          case 'get_service_metrics':
            return await this.handleGetServiceMetrics(args);
          case 'test_service_integration':
            return await this.handleTestServiceIntegration(args);
          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${name}`
            );
        }
      } catch (error) {
        this.logger.error(`Error executing tool ${name}:`, error);
        throw new McpError(
          ErrorCode.InternalError,
          `Error executing tool: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    });
  }

  private async handleListServices(args: any) {
    const services = await this.serviceManager.listServices(args.filter);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(services, null, 2),
        },
      ],
    };
  }

  private async handleGetServiceHealth(args: any) {
    const health = await this.serviceManager.getServiceHealth(args.serviceName);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(health, null, 2),
        },
      ],
    };
  }

  private async handleGetServiceOpenAPI(args: any) {
    const openapi = await this.serviceManager.getServiceOpenAPI(args.serviceName);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(openapi, null, 2),
        },
      ],
    };
  }

  private async handleTestApiEndpoint(args: any) {
    const result = await this.apiClient.testEndpoint(
      args.serviceName,
      args.endpoint,
      args.method,
      args.data,
      args.headers
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  private async handleQueryDatabase(args: any) {
    const result = await this.databaseManager.executeQuery(
      args.serviceName,
      args.query,
      args.params
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  private async handleGetServiceConfig(args: any) {
    const config = await this.configManager.getServiceConfig(args.serviceName);
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(config, null, 2),
        },
      ],
    };
  }

  private async handleGetServiceMetrics(args: any) {
    const metrics = await this.serviceManager.getServiceMetrics(
      args.serviceName,
      args.timeRange
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(metrics, null, 2),
        },
      ],
    };
  }

  private async handleTestServiceIntegration(args: any) {
    const result = await this.serviceManager.testServiceIntegration(
      args.sourceService,
      args.targetService,
      args.testType
    );
    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    this.logger.info('OneFoodDialer MCP Server started');
  }
}

const server = new OneFoodDialerMCPServer();
server.run().catch((error) => {
  console.error('Server failed to start:', error);
  process.exit(1);
});
