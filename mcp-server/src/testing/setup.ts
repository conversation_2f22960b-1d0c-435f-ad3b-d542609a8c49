import dotenv from 'dotenv';
import { Logger } from '../utils/Logger.js';

// Load test environment variables
dotenv.config({ path: '.env.test' });
dotenv.config({ path: '.env' });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce logging noise during tests

// Global test setup
const logger = new Logger('TestSetup');

beforeAll(async () => {
  logger.info('Setting up test environment');
  
  // Add any global test setup here
  // e.g., database connections, mock services, etc.
});

afterAll(async () => {
  logger.info('Tearing down test environment');
  
  // Add any global test cleanup here
  // e.g., close database connections, cleanup mock data, etc.
});

// Global test utilities
declare global {
  var testTimeout: (ms: number) => Promise<void>;
}

(global as any).testTimeout = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock console methods to reduce test output noise
const originalConsole = { ...console };

beforeEach(() => {
  // Optionally mock console methods for cleaner test output
  if (process.env.SILENT_TESTS === 'true') {
    console.log = jest.fn();
    console.info = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  }
});

afterEach(() => {
  // Restore console methods
  if (process.env.SILENT_TESTS === 'true') {
    Object.assign(console, originalConsole);
  }
});

export {};
