import { ServiceManager } from '../services/ServiceManager.js';
import { ApiClient } from '../api/ApiClient.js';
import { DatabaseManager } from '../database/DatabaseManager.js';
import { ConfigManager } from '../config/ConfigManager.js';
import { Logger } from '../utils/Logger.js';

export interface TestCase {
  name: string;
  service: string;
  type: 'api' | 'database' | 'integration';
  config: any;
  expected: any;
}

export interface TestResult {
  testCase: string;
  service: string;
  success: boolean;
  duration: number;
  result: any;
  error?: string;
  timestamp: Date;
}

export interface TestSuite {
  name: string;
  tests: TestCase[];
  setup?: () => Promise<void>;
  teardown?: () => Promise<void>;
}

export class TestRunner {
  private logger: Logger;
  private serviceManager: ServiceManager;
  private apiClient: ApiClient;
  private databaseManager: DatabaseManager;
  private configManager: ConfigManager;

  constructor(
    serviceManager: ServiceManager,
    apiClient: ApiClient,
    databaseManager: DatabaseManager,
    configManager: ConfigManager
  ) {
    this.logger = new Logger('TestRunner');
    this.serviceManager = serviceManager;
    this.apiClient = apiClient;
    this.databaseManager = databaseManager;
    this.configManager = configManager;
  }

  async runTestSuite(suite: TestSuite): Promise<TestResult[]> {
    this.logger.info(`Running test suite: ${suite.name}`);
    const results: TestResult[] = [];

    try {
      // Run setup if provided
      if (suite.setup) {
        await suite.setup();
      }

      // Run all tests
      for (const testCase of suite.tests) {
        const result = await this.runSingleTest(testCase);
        results.push(result);
      }

      // Run teardown if provided
      if (suite.teardown) {
        await suite.teardown();
      }

      const passed = results.filter(r => r.success).length;
      const failed = results.length - passed;
      
      this.logger.info(`Test suite ${suite.name} completed: ${passed} passed, ${failed} failed`);
      
      return results;
    } catch (error) {
      this.logger.error(`Error running test suite ${suite.name}:`, error);
      throw error;
    }
  }

  async runSingleTest(testCase: TestCase): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      this.logger.debug(`Running test: ${testCase.name} for service: ${testCase.service}`);
      
      let result: any;
      
      switch (testCase.type) {
        case 'api':
          result = await this.runApiTest(testCase);
          break;
        case 'database':
          result = await this.runDatabaseTest(testCase);
          break;
        case 'integration':
          result = await this.runIntegrationTest(testCase);
          break;
        default:
          throw new Error(`Unknown test type: ${testCase.type}`);
      }

      const success = this.validateTestResult(result, testCase.expected);
      const duration = Date.now() - startTime;

      return {
        testCase: testCase.name,
        service: testCase.service,
        success,
        duration,
        result,
        timestamp: new Date(),
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        testCase: testCase.name,
        service: testCase.service,
        success: false,
        duration,
        result: null,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      };
    }
  }

  private async runApiTest(testCase: TestCase): Promise<any> {
    const { endpoint, method, data, headers } = testCase.config;
    
    return await this.apiClient.testEndpoint(
      testCase.service,
      endpoint,
      method,
      data,
      headers
    );
  }

  private async runDatabaseTest(testCase: TestCase): Promise<any> {
    const { query, params } = testCase.config;
    
    return await this.databaseManager.executeQuery(
      testCase.service,
      query,
      params
    );
  }

  private async runIntegrationTest(testCase: TestCase): Promise<any> {
    const { targetService, testType } = testCase.config;
    
    return await this.serviceManager.testServiceIntegration(
      testCase.service,
      targetService,
      testType
    );
  }

  private validateTestResult(result: any, expected: any): boolean {
    if (!expected) {
      return true; // No validation criteria
    }

    try {
      // Check status code for API tests
      if (expected.status && result.status !== expected.status) {
        return false;
      }

      // Check response data structure
      if (expected.data) {
        return this.deepCompare(result.data, expected.data);
      }

      // Check success flag
      if (expected.success !== undefined && result.success !== expected.success) {
        return false;
      }

      // Check row count for database tests
      if (expected.rowCount && result.rows?.length !== expected.rowCount) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Error validating test result:', error);
      return false;
    }
  }

  private deepCompare(actual: any, expected: any): boolean {
    if (typeof expected !== typeof actual) {
      return false;
    }

    if (expected === null || actual === null) {
      return expected === actual;
    }

    if (typeof expected === 'object') {
      if (Array.isArray(expected)) {
        if (!Array.isArray(actual) || expected.length !== actual.length) {
          return false;
        }
        return expected.every((item, index) => this.deepCompare(actual[index], item));
      } else {
        const expectedKeys = Object.keys(expected);
        return expectedKeys.every(key => 
          actual.hasOwnProperty(key) && this.deepCompare(actual[key], expected[key])
        );
      }
    }

    return expected === actual;
  }

  async generateServiceTests(serviceName: string): Promise<TestSuite> {
    try {
      const openapi = await this.serviceManager.getServiceOpenAPI(serviceName);
      const tests: TestCase[] = [];

      // Generate API tests from OpenAPI spec
      if (openapi.paths) {
        for (const [path, methods] of Object.entries(openapi.paths)) {
          for (const [method, spec] of Object.entries(methods as any)) {
            if (method === 'get' && !path.includes('{')) {
              // Generate GET tests for endpoints without parameters
              tests.push({
                name: `GET ${path}`,
                service: serviceName,
                type: 'api',
                config: {
                  endpoint: path,
                  method: 'GET',
                },
                expected: {
                  success: true,
                  status: 200,
                },
              });
            }
          }
        }
      }

      // Generate database tests
      const tables = await this.databaseManager.listTables(serviceName);
      for (const table of tables.slice(0, 3)) { // Limit to first 3 tables
        tests.push({
          name: `Database connectivity - ${table}`,
          service: serviceName,
          type: 'database',
          config: {
            query: `SELECT COUNT(*) as count FROM \`${table}\``,
            params: [],
          },
          expected: {
            success: true,
          },
        });
      }

      return {
        name: `Generated tests for ${serviceName}`,
        tests,
      };
    } catch (error) {
      this.logger.error(`Error generating tests for ${serviceName}:`, error);
      return {
        name: `Generated tests for ${serviceName}`,
        tests: [],
      };
    }
  }

  async runServiceHealthTests(): Promise<TestResult[]> {
    const services = await this.serviceManager.listServices();
    const tests: TestCase[] = services.map(service => ({
      name: `Health check - ${service.name}`,
      service: service.name,
      type: 'api',
      config: {
        endpoint: '/health',
        method: 'GET',
      },
      expected: {
        success: true,
        status: 200,
      },
    }));

    const suite: TestSuite = {
      name: 'Service Health Tests',
      tests,
    };

    return await this.runTestSuite(suite);
  }

  async runIntegrationTests(): Promise<TestResult[]> {
    const integrationTests: TestCase[] = [
      {
        name: 'Auth -> Customer Integration',
        service: 'auth-service-v12',
        type: 'integration',
        config: {
          targetService: 'customer-service-v12',
          testType: 'auth',
        },
        expected: {
          success: true,
        },
      },
      {
        name: 'QuickServe -> Payment Integration',
        service: 'quickserve-service-v12',
        type: 'integration',
        config: {
          targetService: 'payment-service-v12',
          testType: 'api_call',
        },
        expected: {
          success: true,
        },
      },
      {
        name: 'Customer -> Notification Integration',
        service: 'customer-service-v12',
        type: 'integration',
        config: {
          targetService: 'notification-service-v12',
          testType: 'queue',
        },
        expected: {
          success: true,
        },
      },
    ];

    const suite: TestSuite = {
      name: 'Service Integration Tests',
      tests: integrationTests,
    };

    return await this.runTestSuite(suite);
  }

  async generateTestReport(results: TestResult[]): Promise<string> {
    const passed = results.filter(r => r.success).length;
    const failed = results.length - passed;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

    let report = `# Test Report\n\n`;
    report += `**Generated:** ${new Date().toISOString()}\n`;
    report += `**Total Tests:** ${results.length}\n`;
    report += `**Passed:** ${passed}\n`;
    report += `**Failed:** ${failed}\n`;
    report += `**Success Rate:** ${((passed / results.length) * 100).toFixed(2)}%\n`;
    report += `**Total Duration:** ${totalDuration}ms\n\n`;

    // Group results by service
    const byService = results.reduce((acc, result) => {
      if (!acc[result.service]) {
        acc[result.service] = [];
      }
      acc[result.service].push(result);
      return acc;
    }, {} as Record<string, TestResult[]>);

    for (const [service, serviceResults] of Object.entries(byService)) {
      const servicePassed = serviceResults.filter(r => r.success).length;
      const serviceFailed = serviceResults.length - servicePassed;

      report += `## ${service}\n\n`;
      report += `- **Tests:** ${serviceResults.length}\n`;
      report += `- **Passed:** ${servicePassed}\n`;
      report += `- **Failed:** ${serviceFailed}\n\n`;

      for (const result of serviceResults) {
        const status = result.success ? '✅' : '❌';
        report += `${status} **${result.testCase}** (${result.duration}ms)\n`;
        
        if (!result.success && result.error) {
          report += `   Error: ${result.error}\n`;
        }
        report += '\n';
      }
    }

    return report;
  }
}
