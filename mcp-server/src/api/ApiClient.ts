import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ConfigManager } from '../config/ConfigManager.js';
import { Logger } from '../utils/Logger.js';
import jwt from 'jsonwebtoken';

export interface ApiTestResult {
  success: boolean;
  status: number;
  statusText: string;
  data: any;
  headers: Record<string, string>;
  responseTime: number;
  error?: string;
}

export interface AuthToken {
  token: string;
  type: 'Bearer' | 'JWT';
  expiresAt?: Date;
}

export class ApiClient {
  private logger: Logger;
  private configManager: ConfigManager;
  private clients: Map<string, AxiosInstance> = new Map();
  private tokens: Map<string, AuthToken> = new Map();

  constructor(configManager: ConfigManager) {
    this.logger = new Logger('ApiClient');
    this.configManager = configManager;
  }

  private getClient(serviceName: string): AxiosInstance {
    if (this.clients.has(serviceName)) {
      return this.clients.get(serviceName)!;
    }

    const config = this.configManager.getServiceConfig(serviceName);
    
    const client = axios.create({
      baseURL: config.url,
      timeout: config.timeout * 1000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'OneFoodDialer-MCP/1.0.0',
      },
    });

    // Add request interceptor for authentication
    client.interceptors.request.use(
      (config) => {
        const token = this.tokens.get(serviceName);
        if (token && this.isTokenValid(token)) {
          config.headers.Authorization = `${token.type} ${token.token}`;
        }
        return config;
      },
      (error) => {
        this.logger.error(`Request interceptor error for ${serviceName}:`, error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    client.interceptors.response.use(
      (response) => response,
      (error) => {
        this.logger.error(`Response error for ${serviceName}:`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );

    this.clients.set(serviceName, client);
    return client;
  }

  private isTokenValid(token: AuthToken): boolean {
    if (!token.expiresAt) {
      return true; // Assume valid if no expiration
    }
    return new Date() < token.expiresAt;
  }

  async testEndpoint(
    serviceName: string,
    endpoint: string,
    method: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiTestResult> {
    const startTime = Date.now();
    
    try {
      const client = this.getClient(serviceName);
      
      const config: AxiosRequestConfig = {
        method: method.toLowerCase() as any,
        url: endpoint,
        headers: headers || {},
      };

      if (data && ['post', 'put', 'patch'].includes(method.toLowerCase())) {
        config.data = data;
      } else if (data && method.toLowerCase() === 'get') {
        config.params = data;
      }

      const response: AxiosResponse = await client.request(config);
      const responseTime = Date.now() - startTime;

      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        headers: response.headers as Record<string, string>,
        responseTime,
      };
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      
      return {
        success: false,
        status: error.response?.status || 0,
        statusText: error.response?.statusText || 'Network Error',
        data: error.response?.data || null,
        headers: error.response?.headers || {},
        responseTime,
        error: error.message,
      };
    }
  }

  async authenticate(serviceName: string, credentials: any): Promise<AuthToken> {
    try {
      const client = this.getClient(serviceName);
      
      // Try different authentication endpoints
      const authEndpoints = ['/auth/login', '/login', '/authenticate'];
      
      for (const endpoint of authEndpoints) {
        try {
          const response = await client.post(endpoint, credentials);
          
          if (response.data.token || response.data.access_token) {
            const token = response.data.token || response.data.access_token;
            const tokenType = response.data.token_type || 'Bearer';
            
            let expiresAt: Date | undefined;
            if (response.data.expires_in) {
              expiresAt = new Date(Date.now() + response.data.expires_in * 1000);
            } else if (response.data.expires_at) {
              expiresAt = new Date(response.data.expires_at);
            }

            const authToken: AuthToken = {
              token,
              type: tokenType as 'Bearer' | 'JWT',
              expiresAt,
            };

            this.tokens.set(serviceName, authToken);
            this.logger.info(`Authentication successful for service: ${serviceName}`);
            
            return authToken;
          }
        } catch (error) {
          // Continue to next endpoint
          continue;
        }
      }
      
      throw new Error('Authentication failed - no valid endpoint found');
    } catch (error) {
      this.logger.error(`Authentication failed for ${serviceName}:`, error);
      throw error;
    }
  }

  async refreshToken(serviceName: string): Promise<AuthToken> {
    try {
      const currentToken = this.tokens.get(serviceName);
      if (!currentToken) {
        throw new Error('No token found to refresh');
      }

      const client = this.getClient(serviceName);
      const response = await client.post('/auth/refresh', {
        refresh_token: currentToken.token,
      });

      if (response.data.token || response.data.access_token) {
        const token = response.data.token || response.data.access_token;
        const tokenType = response.data.token_type || 'Bearer';
        
        let expiresAt: Date | undefined;
        if (response.data.expires_in) {
          expiresAt = new Date(Date.now() + response.data.expires_in * 1000);
        }

        const authToken: AuthToken = {
          token,
          type: tokenType as 'Bearer' | 'JWT',
          expiresAt,
        };

        this.tokens.set(serviceName, authToken);
        this.logger.info(`Token refreshed for service: ${serviceName}`);
        
        return authToken;
      }
      
      throw new Error('Token refresh failed');
    } catch (error) {
      this.logger.error(`Token refresh failed for ${serviceName}:`, error);
      throw error;
    }
  }

  async validateToken(serviceName: string, token?: string): Promise<boolean> {
    try {
      const tokenToValidate = token || this.tokens.get(serviceName)?.token;
      if (!tokenToValidate) {
        return false;
      }

      const client = this.getClient(serviceName);
      const response = await client.get('/auth/validate', {
        headers: {
          Authorization: `Bearer ${tokenToValidate}`,
        },
      });

      return response.status === 200;
    } catch (error) {
      this.logger.error(`Token validation failed for ${serviceName}:`, error);
      return false;
    }
  }

  async makeAuthenticatedRequest(
    serviceName: string,
    endpoint: string,
    method: string,
    data?: any,
    retryAuth: boolean = true
  ): Promise<ApiTestResult> {
    try {
      // First, try the request with existing token
      let result = await this.testEndpoint(serviceName, endpoint, method, data);
      
      // If unauthorized and retry is enabled, try to authenticate
      if (result.status === 401 && retryAuth) {
        this.logger.info(`Attempting re-authentication for ${serviceName}`);
        
        // Try to refresh token first
        try {
          await this.refreshToken(serviceName);
          result = await this.testEndpoint(serviceName, endpoint, method, data);
        } catch (refreshError) {
          // If refresh fails, try full authentication with default credentials
          try {
            await this.authenticate(serviceName, {
              email: '<EMAIL>',
              password: 'admin123',
            });
            result = await this.testEndpoint(serviceName, endpoint, method, data);
          } catch (authError) {
            this.logger.error(`Re-authentication failed for ${serviceName}:`, authError);
          }
        }
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Authenticated request failed for ${serviceName}:`, error);
      throw error;
    }
  }

  async testServiceConnectivity(serviceName: string): Promise<boolean> {
    try {
      const result = await this.testEndpoint(serviceName, '/health', 'GET');
      return result.success && result.status === 200;
    } catch (error) {
      this.logger.error(`Connectivity test failed for ${serviceName}:`, error);
      return false;
    }
  }

  async getServiceInfo(serviceName: string): Promise<any> {
    try {
      const result = await this.testEndpoint(serviceName, '/info', 'GET');
      if (result.success) {
        return result.data;
      }
      
      // Fallback to health endpoint
      const healthResult = await this.testEndpoint(serviceName, '/health', 'GET');
      return healthResult.data;
    } catch (error) {
      this.logger.error(`Failed to get service info for ${serviceName}:`, error);
      throw error;
    }
  }

  clearToken(serviceName: string): void {
    this.tokens.delete(serviceName);
    this.logger.info(`Token cleared for service: ${serviceName}`);
  }

  clearAllTokens(): void {
    this.tokens.clear();
    this.logger.info('All tokens cleared');
  }

  getStoredToken(serviceName: string): AuthToken | undefined {
    return this.tokens.get(serviceName);
  }
}
