#!/usr/bin/env node

import { Command } from 'commander';
import { ServiceManager } from '../services/ServiceManager.js';
import { DatabaseManager } from '../database/DatabaseManager.js';
import { ApiClient } from '../api/ApiClient.js';
import { ConfigManager } from '../config/ConfigManager.js';
import { HealthChecker } from '../monitoring/HealthChecker.js';
import { TestRunner } from '../testing/TestRunner.js';
import { Logger } from '../utils/Logger.js';
import * as fs from 'fs/promises';

const program = new Command();
const logger = new Logger('CLI');

// Initialize managers
const configManager = new ConfigManager();
const serviceManager = new ServiceManager(configManager);
const databaseManager = new DatabaseManager(configManager);
const apiClient = new ApiClient(configManager);
const healthChecker = new HealthChecker(serviceManager, databaseManager, apiClient, configManager);
const testRunner = new TestRunner(serviceManager, apiClient, databaseManager, configManager);

program
  .name('mcp-cli')
  .description('OneFoodDialer Services MCP CLI')
  .version('1.0.0');

// Service commands
program
  .command('list-services')
  .description('List all available services')
  .option('-f, --filter <filter>', 'Filter services by name or status')
  .action(async (options) => {
    try {
      const services = await serviceManager.listServices(options.filter);
      console.table(services);
    } catch (error) {
      logger.error('Error listing services:', error);
      process.exit(1);
    }
  });

program
  .command('health')
  .description('Check health of services')
  .option('-s, --service <service>', 'Check specific service')
  .option('-a, --all', 'Check all services')
  .action(async (options) => {
    try {
      if (options.service) {
        const health = await serviceManager.getServiceHealth(options.service);
        console.log(JSON.stringify(health, null, 2));
      } else {
        const systemHealth = await healthChecker.performFullHealthCheck();
        console.log(JSON.stringify(systemHealth, null, 2));
      }
    } catch (error) {
      logger.error('Error checking health:', error);
      process.exit(1);
    }
  });

program
  .command('test-api')
  .description('Test API endpoint')
  .requiredOption('-s, --service <service>', 'Service name')
  .requiredOption('-e, --endpoint <endpoint>', 'API endpoint')
  .requiredOption('-m, --method <method>', 'HTTP method')
  .option('-d, --data <data>', 'Request data (JSON string)')
  .option('-h, --headers <headers>', 'Request headers (JSON string)')
  .action(async (options) => {
    try {
      const data = options.data ? JSON.parse(options.data) : undefined;
      const headers = options.headers ? JSON.parse(options.headers) : undefined;
      
      const result = await apiClient.testEndpoint(
        options.service,
        options.endpoint,
        options.method,
        data,
        headers
      );
      
      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      logger.error('Error testing API:', error);
      process.exit(1);
    }
  });

program
  .command('query-db')
  .description('Execute database query')
  .requiredOption('-s, --service <service>', 'Service name')
  .requiredOption('-q, --query <query>', 'SQL query')
  .option('-p, --params <params>', 'Query parameters (JSON array)')
  .action(async (options) => {
    try {
      const params = options.params ? JSON.parse(options.params) : [];
      const result = await databaseManager.executeQuery(
        options.service,
        options.query,
        params
      );
      
      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      logger.error('Error executing query:', error);
      process.exit(1);
    }
  });

program
  .command('list-tables')
  .description('List database tables for a service')
  .requiredOption('-s, --service <service>', 'Service name')
  .action(async (options) => {
    try {
      const tables = await databaseManager.listTables(options.service);
      console.log('Tables:');
      tables.forEach(table => console.log(`  - ${table}`));
    } catch (error) {
      logger.error('Error listing tables:', error);
      process.exit(1);
    }
  });

program
  .command('describe-table')
  .description('Describe database table schema')
  .requiredOption('-s, --service <service>', 'Service name')
  .requiredOption('-t, --table <table>', 'Table name')
  .action(async (options) => {
    try {
      const schema = await databaseManager.getTableSchema(options.service, options.table);
      console.table(schema);
    } catch (error) {
      logger.error('Error describing table:', error);
      process.exit(1);
    }
  });

program
  .command('run-tests')
  .description('Run test suite')
  .option('-s, --service <service>', 'Test specific service')
  .option('-t, --type <type>', 'Test type (health, integration, generated)')
  .option('-r, --report <file>', 'Generate report file')
  .action(async (options) => {
    try {
      let results: any[] = [];
      
      if (options.type === 'health') {
        results = await testRunner.runServiceHealthTests();
      } else if (options.type === 'integration') {
        results = await testRunner.runIntegrationTests();
      } else if (options.service) {
        const suite = await testRunner.generateServiceTests(options.service);
        results = await testRunner.runTestSuite(suite);
      } else {
        // Run all health tests by default
        results = await testRunner.runServiceHealthTests();
      }
      
      console.log(JSON.stringify(results, null, 2));
      
      if (options.report) {
        const report = await testRunner.generateTestReport(results);
        await fs.writeFile(options.report, report);
        console.log(`Report saved to: ${options.report}`);
      }
    } catch (error) {
      logger.error('Error running tests:', error);
      process.exit(1);
    }
  });

program
  .command('get-config')
  .description('Get service configuration')
  .requiredOption('-s, --service <service>', 'Service name')
  .action(async (options) => {
    try {
      const config = configManager.getServiceConfig(options.service);
      console.log(JSON.stringify(config, null, 2));
    } catch (error) {
      logger.error('Error getting config:', error);
      process.exit(1);
    }
  });

program
  .command('get-openapi')
  .description('Get OpenAPI specification')
  .requiredOption('-s, --service <service>', 'Service name')
  .option('-o, --output <file>', 'Output file')
  .action(async (options) => {
    try {
      const openapi = await serviceManager.getServiceOpenAPI(options.service);
      const output = JSON.stringify(openapi, null, 2);
      
      if (options.output) {
        await fs.writeFile(options.output, output);
        console.log(`OpenAPI spec saved to: ${options.output}`);
      } else {
        console.log(output);
      }
    } catch (error) {
      logger.error('Error getting OpenAPI spec:', error);
      process.exit(1);
    }
  });

program
  .command('backup-db')
  .description('Backup service database')
  .requiredOption('-s, --service <service>', 'Service name')
  .option('-o, --output <file>', 'Output file')
  .option('-t, --tables <tables>', 'Comma-separated list of tables')
  .action(async (options) => {
    try {
      const tables = options.tables ? options.tables.split(',') : undefined;
      const backup = await databaseManager.backup(options.service, tables);
      
      const outputFile = options.output || `${options.service}-backup-${Date.now()}.sql`;
      await fs.writeFile(outputFile, backup);
      console.log(`Database backup saved to: ${outputFile}`);
    } catch (error) {
      logger.error('Error creating backup:', error);
      process.exit(1);
    }
  });

program
  .command('monitor')
  .description('Start health monitoring')
  .option('-i, --interval <minutes>', 'Check interval in minutes', '5')
  .action(async (options) => {
    try {
      const interval = parseInt(options.interval);
      console.log(`Starting health monitoring with ${interval} minute intervals...`);
      console.log('Press Ctrl+C to stop');
      
      await healthChecker.startMonitoring(interval);
      
      // Keep the process running
      process.on('SIGINT', () => {
        console.log('\nStopping health monitoring...');
        healthChecker.stopMonitoring();
        process.exit(0);
      });
      
      // Keep alive
      setInterval(() => {}, 1000);
    } catch (error) {
      logger.error('Error starting monitoring:', error);
      process.exit(1);
    }
  });

program
  .command('metrics')
  .description('Get system metrics')
  .option('-s, --service <service>', 'Get metrics for specific service')
  .option('-t, --time-range <range>', 'Time range (1h, 24h, 7d)', '1h')
  .action(async (options) => {
    try {
      if (options.service) {
        const metrics = await serviceManager.getServiceMetrics(options.service, options.timeRange);
        console.log(JSON.stringify(metrics, null, 2));
      } else {
        const systemMetrics = await healthChecker.getSystemMetrics();
        console.log(JSON.stringify(systemMetrics, null, 2));
      }
    } catch (error) {
      logger.error('Error getting metrics:', error);
      process.exit(1);
    }
  });

// Parse command line arguments
program.parse();
