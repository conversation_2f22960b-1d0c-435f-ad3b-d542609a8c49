import mysql from 'mysql2/promise';
import { ConfigManager, DatabaseConfig } from '../config/ConfigManager.js';
import { Logger } from '../utils/Logger.js';

export interface QueryResult {
  rows: any[];
  fields: any[];
  affectedRows?: number;
  insertId?: number;
  executionTime: number;
}

export class DatabaseManager {
  private logger: Logger;
  private configManager: ConfigManager;
  private connections: Map<string, mysql.Pool> = new Map();

  constructor(configManager: ConfigManager) {
    this.logger = new Logger('DatabaseManager');
    this.configManager = configManager;
  }

  private async getConnection(serviceName: string): Promise<mysql.Pool> {
    if (this.connections.has(serviceName)) {
      return this.connections.get(serviceName)!;
    }

    const dbConfig = this.configManager.getDatabaseConfig(serviceName);
    if (!dbConfig) {
      throw new Error(`No database configuration found for service: ${serviceName}`);
    }

    try {
      const pool = mysql.createPool({
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.username,
        password: dbConfig.password,
        database: dbConfig.database,
        waitForConnections: true,
        connectionLimit: 10,
        queueLimit: 0,
      });

      // Test the connection
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();

      this.connections.set(serviceName, pool);
      this.logger.info(`Database connection established for service: ${serviceName}`);
      
      return pool;
    } catch (error) {
      this.logger.error(`Failed to connect to database for service ${serviceName}:`, error);
      throw error;
    }
  }

  async executeQuery(
    serviceName: string,
    query: string,
    params: any[] = []
  ): Promise<QueryResult> {
    const startTime = Date.now();
    
    try {
      const pool = await this.getConnection(serviceName);
      const [rows, fields] = await pool.execute(query, params);
      
      const executionTime = Date.now() - startTime;
      
      this.logger.info(`Query executed for ${serviceName} in ${executionTime}ms`);
      
      return {
        rows: Array.isArray(rows) ? rows : [],
        fields: fields || [],
        affectedRows: (rows as any).affectedRows,
        insertId: (rows as any).insertId,
        executionTime,
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.logger.error(`Query failed for ${serviceName} after ${executionTime}ms:`, error);
      throw error;
    }
  }

  async testConnection(serviceName: string): Promise<boolean> {
    try {
      const pool = await this.getConnection(serviceName);
      const connection = await pool.getConnection();
      await connection.ping();
      connection.release();
      return true;
    } catch (error) {
      this.logger.error(`Database connection test failed for ${serviceName}:`, error);
      return false;
    }
  }

  async getTableSchema(serviceName: string, tableName: string): Promise<any[]> {
    try {
      const query = `
        SELECT 
          COLUMN_NAME,
          DATA_TYPE,
          IS_NULLABLE,
          COLUMN_DEFAULT,
          COLUMN_KEY,
          EXTRA,
          COLUMN_COMMENT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?
        ORDER BY ORDINAL_POSITION
      `;
      
      const dbConfig = this.configManager.getDatabaseConfig(serviceName);
      if (!dbConfig) {
        throw new Error(`No database configuration found for service: ${serviceName}`);
      }

      const result = await this.executeQuery(serviceName, query, [dbConfig.database, tableName]);
      return result.rows;
    } catch (error) {
      this.logger.error(`Failed to get table schema for ${serviceName}.${tableName}:`, error);
      throw error;
    }
  }

  async listTables(serviceName: string): Promise<string[]> {
    try {
      const dbConfig = this.configManager.getDatabaseConfig(serviceName);
      if (!dbConfig) {
        throw new Error(`No database configuration found for service: ${serviceName}`);
      }

      const query = `
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = ? AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
      `;
      
      const result = await this.executeQuery(serviceName, query, [dbConfig.database]);
      return result.rows.map((row: any) => row.TABLE_NAME);
    } catch (error) {
      this.logger.error(`Failed to list tables for ${serviceName}:`, error);
      throw error;
    }
  }

  async getTableRowCount(serviceName: string, tableName: string): Promise<number> {
    try {
      const query = `SELECT COUNT(*) as count FROM \`${tableName}\``;
      const result = await this.executeQuery(serviceName, query);
      return result.rows[0]?.count || 0;
    } catch (error) {
      this.logger.error(`Failed to get row count for ${serviceName}.${tableName}:`, error);
      throw error;
    }
  }

  async executeMigration(serviceName: string, migrationSql: string): Promise<QueryResult> {
    try {
      this.logger.info(`Executing migration for service: ${serviceName}`);
      
      // Split migration into individual statements
      const statements = migrationSql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      const results: QueryResult[] = [];
      
      for (const statement of statements) {
        const result = await this.executeQuery(serviceName, statement);
        results.push(result);
      }

      // Return combined results
      return {
        rows: results.flatMap(r => r.rows),
        fields: results[0]?.fields || [],
        affectedRows: results.reduce((sum, r) => sum + (r.affectedRows || 0), 0),
        executionTime: results.reduce((sum, r) => sum + r.executionTime, 0),
      };
    } catch (error) {
      this.logger.error(`Migration failed for ${serviceName}:`, error);
      throw error;
    }
  }

  async backup(serviceName: string, tables?: string[]): Promise<string> {
    try {
      const dbConfig = this.configManager.getDatabaseConfig(serviceName);
      if (!dbConfig) {
        throw new Error(`No database configuration found for service: ${serviceName}`);
      }

      const tablesToBackup = tables || await this.listTables(serviceName);
      let backupSql = `-- Database backup for ${serviceName}\n`;
      backupSql += `-- Generated on ${new Date().toISOString()}\n\n`;

      for (const table of tablesToBackup) {
        // Get table structure
        const createTableQuery = `SHOW CREATE TABLE \`${table}\``;
        const createResult = await this.executeQuery(serviceName, createTableQuery);
        
        if (createResult.rows.length > 0) {
          backupSql += `-- Table structure for ${table}\n`;
          backupSql += `DROP TABLE IF EXISTS \`${table}\`;\n`;
          backupSql += createResult.rows[0]['Create Table'] + ';\n\n';
        }

        // Get table data
        const dataQuery = `SELECT * FROM \`${table}\``;
        const dataResult = await this.executeQuery(serviceName, dataQuery);
        
        if (dataResult.rows.length > 0) {
          backupSql += `-- Data for table ${table}\n`;
          
          for (const row of dataResult.rows) {
            const columns = Object.keys(row).map(col => `\`${col}\``).join(', ');
            const values = Object.values(row)
              .map(val => {
                if (val === null || val === undefined) return 'NULL';
                if (typeof val === 'object') return `'${JSON.stringify(val).replace(/'/g, "''")}'`;
                if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
                if (typeof val === 'number' || typeof val === 'boolean') return String(val);
                // eslint-disable-next-line @typescript-eslint/no-base-to-string
                return `'${String(val).replace(/'/g, "''")}'`;
              })
              .join(', ');
            
            backupSql += `INSERT INTO \`${table}\` (${columns}) VALUES (${values});\n`;
          }
          backupSql += '\n';
        }
      }

      return backupSql;
    } catch (error) {
      this.logger.error(`Backup failed for ${serviceName}:`, error);
      throw error;
    }
  }

  async closeConnections(): Promise<void> {
    for (const [serviceName, pool] of this.connections) {
      try {
        await pool.end();
        this.logger.info(`Closed database connection for service: ${serviceName}`);
      } catch (error) {
        this.logger.error(`Error closing connection for ${serviceName}:`, error);
      }
    }
    this.connections.clear();
  }

  async getConnectionStatus(): Promise<Record<string, boolean>> {
    const status: Record<string, boolean> = {};
    
    for (const [serviceName] of this.connections) {
      status[serviceName] = await this.testConnection(serviceName);
    }
    
    return status;
  }
}
