import { ServiceManager } from '../services/ServiceManager.js';
import { DatabaseManager } from '../database/DatabaseManager.js';
import { ApiClient } from '../api/ApiClient.js';
import { ConfigManager } from '../config/ConfigManager.js';
import { Logger } from '../utils/Logger.js';
import cron from 'node-cron';

export interface HealthStatus {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded' | 'unknown';
  lastChecked: Date;
  responseTime: number;
  checks: {
    api: boolean;
    database: boolean;
    redis?: boolean;
    dependencies?: boolean;
  };
  error?: string;
}

export interface SystemHealth {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: HealthStatus[];
  timestamp: Date;
  summary: {
    total: number;
    healthy: number;
    unhealthy: number;
    degraded: number;
    unknown: number;
  };
}

export class HealthChecker {
  private logger: Logger;
  private serviceManager: ServiceManager;
  private databaseManager: DatabaseManager;
  private apiClient: ApiClient;
  private configManager: ConfigManager;
  private healthCache: Map<string, HealthStatus> = new Map();
  private isRunning: boolean = false;

  constructor(
    serviceManager: ServiceManager,
    databaseManager: DatabaseManager,
    apiClient: ApiClient,
    configManager: ConfigManager
  ) {
    this.logger = new Logger('HealthChecker');
    this.serviceManager = serviceManager;
    this.databaseManager = databaseManager;
    this.apiClient = apiClient;
    this.configManager = configManager;
  }

  async startMonitoring(intervalMinutes: number = 5): Promise<void> {
    if (this.isRunning) {
      this.logger.warn('Health monitoring is already running');
      return;
    }

    this.isRunning = true;
    this.logger.info(`Starting health monitoring with ${intervalMinutes} minute intervals`);

    // Initial health check
    await this.performFullHealthCheck();

    // Schedule periodic health checks
    const cronExpression = `*/${intervalMinutes} * * * *`;
    cron.schedule(cronExpression, async () => {
      try {
        await this.performFullHealthCheck();
      } catch (error) {
        this.logger.error('Error during scheduled health check:', error);
      }
    });
  }

  stopMonitoring(): void {
    this.isRunning = false;
    this.logger.info('Health monitoring stopped');
  }

  async performFullHealthCheck(): Promise<SystemHealth> {
    this.logger.info('Performing full system health check');
    const startTime = Date.now();

    try {
      const services = await this.serviceManager.listServices();
      const healthStatuses: HealthStatus[] = [];

      // Check each service in parallel
      const healthPromises = services.map(service => 
        this.checkServiceHealth(service.name)
      );

      const results = await Promise.allSettled(healthPromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          healthStatuses.push(result.value);
        } else {
          const serviceName = services[index].name;
          healthStatuses.push({
            service: serviceName,
            status: 'unknown',
            lastChecked: new Date(),
            responseTime: 0,
            checks: {
              api: false,
              database: false,
            },
            error: result.reason?.message || 'Unknown error',
          });
        }
      });

      // Calculate summary
      const summary = {
        total: healthStatuses.length,
        healthy: healthStatuses.filter(s => s.status === 'healthy').length,
        unhealthy: healthStatuses.filter(s => s.status === 'unhealthy').length,
        degraded: healthStatuses.filter(s => s.status === 'degraded').length,
        unknown: healthStatuses.filter(s => s.status === 'unknown').length,
      };

      // Determine overall system health
      let overall: 'healthy' | 'degraded' | 'unhealthy';
      if (summary.unhealthy > 0 || summary.unknown > summary.total * 0.5) {
        overall = 'unhealthy';
      } else if (summary.degraded > 0 || summary.unknown > 0) {
        overall = 'degraded';
      } else {
        overall = 'healthy';
      }

      const systemHealth: SystemHealth = {
        overall,
        services: healthStatuses,
        timestamp: new Date(),
        summary,
      };

      const duration = Date.now() - startTime;
      this.logger.info(`Health check completed in ${duration}ms - Overall: ${overall}`);

      return systemHealth;
    } catch (error) {
      this.logger.error('Error during full health check:', error);
      throw error;
    }
  }

  async checkServiceHealth(serviceName: string): Promise<HealthStatus> {
    const startTime = Date.now();
    const healthStatus: HealthStatus = {
      service: serviceName,
      status: 'unknown',
      lastChecked: new Date(),
      responseTime: 0,
      checks: {
        api: false,
        database: false,
      },
    };

    try {
      // Check API health
      const apiHealthy = await this.checkApiHealth(serviceName);
      healthStatus.checks.api = apiHealthy;

      // Check database connectivity
      const dbHealthy = await this.checkDatabaseHealth(serviceName);
      healthStatus.checks.database = dbHealthy;

      // Check Redis if configured
      const redisHealthy = await this.checkRedisHealth(serviceName);
      if (redisHealthy !== null) {
        healthStatus.checks.redis = redisHealthy;
      }

      // Check service dependencies
      const dependenciesHealthy = await this.checkServiceDependencies(serviceName);
      if (dependenciesHealthy !== null) {
        healthStatus.checks.dependencies = dependenciesHealthy;
      }

      // Determine overall service status
      const criticalChecks = [healthStatus.checks.api, healthStatus.checks.database];
      const optionalChecks = [healthStatus.checks.redis, healthStatus.checks.dependencies].filter(c => c !== undefined);

      if (criticalChecks.every(check => check === true)) {
        if (optionalChecks.length === 0 || optionalChecks.every(check => check === true)) {
          healthStatus.status = 'healthy';
        } else {
          healthStatus.status = 'degraded';
        }
      } else {
        healthStatus.status = 'unhealthy';
      }

      healthStatus.responseTime = Date.now() - startTime;
      
      // Cache the result
      this.healthCache.set(serviceName, healthStatus);

      return healthStatus;
    } catch (error) {
      healthStatus.status = 'unhealthy';
      healthStatus.responseTime = Date.now() - startTime;
      healthStatus.error = error instanceof Error ? error.message : 'Unknown error';
      
      this.logger.error(`Health check failed for ${serviceName}:`, error);
      return healthStatus;
    }
  }

  private async checkApiHealth(serviceName: string): Promise<boolean> {
    try {
      const result = await this.apiClient.testEndpoint(serviceName, '/health', 'GET');
      return result.success && result.status === 200;
    } catch (error) {
      return false;
    }
  }

  private async checkDatabaseHealth(serviceName: string): Promise<boolean> {
    try {
      return await this.databaseManager.testConnection(serviceName);
    } catch (error) {
      return false;
    }
  }

  private async checkRedisHealth(serviceName: string): Promise<boolean | null> {
    try {
      const redisConfig = this.configManager.getRedisConfig(serviceName);
      if (!redisConfig) {
        return null; // Redis not configured for this service
      }

      // Test Redis connection
      const result = await this.apiClient.testEndpoint(serviceName, '/health/redis', 'GET');
      return result.success && result.status === 200;
    } catch (error) {
      return false;
    }
  }

  private async checkServiceDependencies(serviceName: string): Promise<boolean | null> {
    try {
      // Check if service has dependencies endpoint
      const result = await this.apiClient.testEndpoint(serviceName, '/health/dependencies', 'GET');
      if (result.success && result.status === 200) {
        return result.data?.all_healthy === true;
      }
      return null; // Dependencies check not available
    } catch (error) {
      return null;
    }
  }

  getCachedHealth(serviceName: string): HealthStatus | undefined {
    return this.healthCache.get(serviceName);
  }

  getAllCachedHealth(): HealthStatus[] {
    return Array.from(this.healthCache.values());
  }

  clearHealthCache(): void {
    this.healthCache.clear();
    this.logger.info('Health cache cleared');
  }

  async getServiceUptime(serviceName: string): Promise<number | null> {
    try {
      const result = await this.apiClient.testEndpoint(serviceName, '/health/uptime', 'GET');
      if (result.success && result.data?.uptime) {
        return result.data.uptime;
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  async getSystemMetrics(): Promise<any> {
    try {
      const systemHealth = await this.performFullHealthCheck();
      const services = await this.serviceManager.listServices();
      
      const metrics = {
        system: systemHealth,
        services: await Promise.all(
          services.map(async (service) => ({
            name: service.name,
            health: this.getCachedHealth(service.name),
            uptime: await this.getServiceUptime(service.name),
            metrics: await this.serviceManager.getServiceMetrics(service.name, '1h'),
          }))
        ),
        timestamp: new Date(),
      };

      return metrics;
    } catch (error) {
      this.logger.error('Error getting system metrics:', error);
      throw error;
    }
  }
}
