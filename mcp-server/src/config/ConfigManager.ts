import * as fs from 'fs/promises';
import * as path from 'path';
import { Logger } from '../utils/Logger.js';
import dotenv from 'dotenv';

export interface ServiceConfig {
  name: string;
  url: string;
  timeout: number;
  retries: number;
  database?: DatabaseConfig;
  redis?: RedisConfig;
  auth?: AuthConfig;
}

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  database: number;
}

export interface AuthConfig {
  jwt_secret: string;
  jwt_algorithm: string;
  token_ttl: number;
}

export class ConfigManager {
  private logger: Logger;
  private configs: Map<string, ServiceConfig> = new Map();
  private servicesPath: string;

  constructor() {
    this.logger = new Logger('ConfigManager');
    this.servicesPath = path.join(process.cwd(), '../services');
    this.loadEnvironmentVariables();
    this.initializeConfigs();
  }

  private loadEnvironmentVariables() {
    // Load environment variables from .env files
    const envPaths = [
      path.join(process.cwd(), '.env'),
      path.join(this.servicesPath, '.env'),
    ];

    for (const envPath of envPaths) {
      try {
        dotenv.config({ path: envPath });
      } catch (error) {
        // Ignore if .env file doesn't exist
      }
    }
  }

  private async initializeConfigs() {
    try {
      // Load service configurations from services/config/services.php
      const servicesConfigPath = path.join(this.servicesPath, 'config', 'services.php');
      
      try {
        const servicesConfig = await this.parsePhpConfig(servicesConfigPath);
        this.loadServicesFromPhpConfig(servicesConfig);
      } catch (error) {
        this.logger.warn('Could not load services.php config, using defaults');
        this.loadDefaultConfigs();
      }

      // Load individual service configurations
      await this.loadIndividualServiceConfigs();
    } catch (error) {
      this.logger.error('Error initializing configs:', error);
    }
  }

  private async parsePhpConfig(filePath: string): Promise<any> {
    // Simple PHP array parser for services.php
    const content = await fs.readFile(filePath, 'utf-8');
    
    // Extract service configurations using regex
    const serviceMatches = content.match(/'(\w+)'\s*=>\s*\[([\s\S]*?)\]/g);
    const services: any = {};

    if (serviceMatches) {
      for (const match of serviceMatches) {
        const serviceMatch = match.match(/'(\w+)'\s*=>\s*\[([\s\S]*?)\]/);
        if (serviceMatch) {
          const serviceName = serviceMatch[1];
          const configContent = serviceMatch[2];
          
          // Parse URL
          const urlMatch = configContent.match(/'url'\s*=>\s*env\('([^']+)',\s*'([^']+)'\)/);
          const timeoutMatch = configContent.match(/'timeout'\s*=>\s*env\('([^']+)',\s*(\d+)\)/);
          const retriesMatch = configContent.match(/'retries'\s*=>\s*env\('([^']+)',\s*(\d+)\)/);

          services[serviceName] = {
            url: urlMatch ? (process.env[urlMatch[1]] || urlMatch[2]) : `http://${serviceName}-service-v12:8000/api`,
            timeout: timeoutMatch ? parseInt(process.env[timeoutMatch[1]] || timeoutMatch[2]) : 30,
            retries: retriesMatch ? parseInt(process.env[retriesMatch[1]] || retriesMatch[2]) : 3,
          };
        }
      }
    }

    return services;
  }

  private loadServicesFromPhpConfig(servicesConfig: any) {
    for (const [serviceName, config] of Object.entries(servicesConfig)) {
      this.configs.set(serviceName, {
        name: serviceName,
        url: (config as any).url,
        timeout: (config as any).timeout,
        retries: (config as any).retries,
      });
    }
  }

  private loadDefaultConfigs() {
    const defaultServices = [
      'auth-service-v12',
      'customer-service-v12',
      'payment-service-v12',
      'quickserve-service-v12',
      'admin-service-v12',
      'analytics-service-v12',
      'catalogue-service-v12',
      'kitchen-service-v12',
      'delivery-service-v12',
      'meal-service-v12',
      'subscription-service-v12',
      'notification-service-v12',
      'invoice-service-v12',
      'misscall-service-v12',
    ];

    for (const serviceName of defaultServices) {
      this.configs.set(serviceName, {
        name: serviceName,
        url: `http://${serviceName}:8000/api`,
        timeout: 30,
        retries: 3,
      });
    }
  }

  private async loadIndividualServiceConfigs() {
    try {
      const serviceDirs = await fs.readdir(this.servicesPath);

      for (const dir of serviceDirs) {
        if (dir.endsWith('-v12') || dir.endsWith('-service')) {
          await this.loadServiceConfig(dir);
        }
      }
    } catch (error) {
      this.logger.error('Error loading individual service configs:', error);
    }
  }

  private async loadServiceConfig(serviceName: string) {
    try {
      const servicePath = path.join(this.servicesPath, serviceName);
      
      // Try to load .env file from service directory
      const envPath = path.join(servicePath, '.env');
      try {
        const envContent = await fs.readFile(envPath, 'utf-8');
        const envVars = this.parseEnvFile(envContent);
        
        const existingConfig = this.configs.get(serviceName) || {
          name: serviceName,
          url: `http://${serviceName}:8000/api`,
          timeout: 30,
          retries: 3,
        };

        // Update config with database settings
        if (envVars.DB_HOST) {
          existingConfig.database = {
            host: envVars.DB_HOST,
            port: parseInt(envVars.DB_PORT || '3306'),
            database: envVars.DB_DATABASE || serviceName.replace('-', '_'),
            username: envVars.DB_USERNAME || 'root',
            password: envVars.DB_PASSWORD || '',
          };
        }

        // Update config with Redis settings
        if (envVars.REDIS_HOST) {
          existingConfig.redis = {
            host: envVars.REDIS_HOST,
            port: parseInt(envVars.REDIS_PORT || '6379'),
            password: envVars.REDIS_PASSWORD,
            database: parseInt(envVars.REDIS_DB || '0'),
          };
        }

        // Update config with Auth settings
        if (envVars.JWT_SECRET) {
          existingConfig.auth = {
            jwt_secret: envVars.JWT_SECRET,
            jwt_algorithm: envVars.JWT_ALGORITHM || 'HS256',
            token_ttl: parseInt(envVars.JWT_TTL || '3600'),
          };
        }

        this.configs.set(serviceName, existingConfig);
      } catch {
        // .env file doesn't exist or can't be read
      }
    } catch (error) {
      this.logger.error(`Error loading config for service ${serviceName}:`, error);
    }
  }

  private parseEnvFile(content: string): Record<string, string> {
    const envVars: Record<string, string> = {};
    const lines = content.split('\n');

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^["']|["']$/g, '');
          envVars[key.trim()] = value;
        }
      }
    }

    return envVars;
  }

  getServiceConfig(serviceName: string): ServiceConfig {
    const config = this.configs.get(serviceName);
    if (!config) {
      // Return default config if not found
      return {
        name: serviceName,
        url: `http://${serviceName}:8000/api`,
        timeout: 30,
        retries: 3,
      };
    }
    return config;
  }

  getAllServiceConfigs(): ServiceConfig[] {
    return Array.from(this.configs.values());
  }

  updateServiceConfig(serviceName: string, config: Partial<ServiceConfig>) {
    const existingConfig = this.getServiceConfig(serviceName);
    const updatedConfig = { ...existingConfig, ...config };
    this.configs.set(serviceName, updatedConfig);
  }

  getEnvironmentVariable(key: string, defaultValue?: string): string | undefined {
    return process.env[key] || defaultValue;
  }

  getDatabaseConfig(serviceName: string): DatabaseConfig | undefined {
    const config = this.getServiceConfig(serviceName);
    return config.database;
  }

  getRedisConfig(serviceName: string): RedisConfig | undefined {
    const config = this.getServiceConfig(serviceName);
    return config.redis;
  }

  getAuthConfig(serviceName: string): AuthConfig | undefined {
    const config = this.getServiceConfig(serviceName);
    return config.auth;
  }
}
