# OneFoodDialer Services MCP Server

A Model Context Protocol (MCP) server for managing and interacting with the OneFoodDialer 2025 microservices architecture.

## Features

### 🔍 Service Discovery & Management
- **List Services**: Discover all available microservices and their status
- **Health Monitoring**: Real-time health checks for all services
- **Service Metrics**: Performance monitoring and analytics
- **Configuration Management**: Centralized service configuration

### 🛠 API Testing & Integration
- **Endpoint Testing**: Test any API endpoint with custom parameters
- **Authentication**: Automatic JWT token management
- **Integration Testing**: Test service-to-service communication
- **OpenAPI Integration**: Access and validate API specifications

### 💾 Database Operations
- **Query Execution**: Run SQL queries across service databases
- **Schema Inspection**: View table structures and relationships
- **Migration Support**: Execute database migrations
- **Backup & Restore**: Database backup and restoration tools

### 📊 Monitoring & Analytics
- **Performance Metrics**: Response times, error rates, throughput
- **Service Dependencies**: Visualize service relationships
- **Health Dashboards**: Real-time service status monitoring
- **Log Aggregation**: Centralized logging and analysis

## Architecture

The MCP server is built with TypeScript and provides tools for:

- **14 Core Services**: Auth, Customer, Payment, QuickServe, Admin, Analytics, Catalogue, Kitchen, Delivery, Meal, Subscription, Notification, Invoice, MissCall
- **Integration Layers**: Service clients and middleware
- **Infrastructure**: Kong Gateway, Redis, MySQL databases
- **Shared Components**: Common utilities and configurations

## Installation

```bash
# Clone and navigate to MCP server directory
cd mcp-server

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit configuration
nano .env

# Build the project
npm run build

# Start the server
npm start
```

## Development

```bash
# Development mode with hot reload
npm run dev

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

## Configuration

### Environment Variables

Key configuration options in `.env`:

```env
# Service Discovery
SERVICES_PATH=../services

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your-password

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your-jwt-secret
JWT_ALGORITHM=HS256
JWT_TTL=3600

# Service URLs
AUTH_SERVICE_URL=http://localhost:8001/api
CUSTOMER_SERVICE_URL=http://localhost:8002/api
# ... other services
```

### Service Configuration

The server automatically discovers services from:
- `services/config/services.php` - Laravel service configuration
- Individual service `.env` files
- OpenAPI specifications (`openapi.yaml`)
- Composer configurations (`composer.json`)

## Available Tools

### Service Management

#### `list_services`
List all available microservices and their status.

```json
{
  "name": "list_services",
  "arguments": {
    "filter": "healthy" // Optional: filter by name or status
  }
}
```

#### `get_service_health`
Check detailed health status of a specific service.

```json
{
  "name": "get_service_health",
  "arguments": {
    "serviceName": "auth-service-v12"
  }
}
```

#### `get_service_openapi`
Retrieve OpenAPI specification for a service.

```json
{
  "name": "get_service_openapi",
  "arguments": {
    "serviceName": "quickserve-service-v12"
  }
}
```

### API Testing

#### `test_api_endpoint`
Test any API endpoint with custom parameters.

```json
{
  "name": "test_api_endpoint",
  "arguments": {
    "serviceName": "customer-service-v12",
    "endpoint": "/customers",
    "method": "GET",
    "data": {"page": 1, "limit": 10},
    "headers": {"Authorization": "Bearer token"}
  }
}
```

### Database Operations

#### `query_database`
Execute SQL queries on service databases.

```json
{
  "name": "query_database",
  "arguments": {
    "serviceName": "customer-service-v12",
    "query": "SELECT * FROM customers LIMIT 10",
    "params": []
  }
}
```

### Configuration & Monitoring

#### `get_service_config`
Retrieve configuration for a service.

```json
{
  "name": "get_service_config",
  "arguments": {
    "serviceName": "payment-service-v12"
  }
}
```

#### `get_service_metrics`
Get performance metrics for a service.

```json
{
  "name": "get_service_metrics",
  "arguments": {
    "serviceName": "quickserve-service-v12",
    "timeRange": "24h"
  }
}
```

### Integration Testing

#### `test_service_integration`
Test integration between services.

```json
{
  "name": "test_service_integration",
  "arguments": {
    "sourceService": "quickserve-service-v12",
    "targetService": "payment-service-v12",
    "testType": "auth"
  }
}
```

## Service Architecture

### Core Services (v12)

1. **auth-service-v12** - Authentication & JWT management
2. **customer-service-v12** - Customer profiles & management
3. **payment-service-v12** - Payment processing & wallets
4. **quickserve-service-v12** - Order management & quick service
5. **admin-service-v12** - Administrative functions
6. **analytics-service-v12** - Analytics & reporting
7. **catalogue-service-v12** - Product catalog & menus
8. **kitchen-service-v12** - Kitchen operations
9. **delivery-service-v12** - Delivery management
10. **meal-service-v12** - Meal planning
11. **subscription-service-v12** - Subscription management
12. **notification-service-v12** - Notifications & messaging
13. **invoice-service-v12** - Invoicing & billing
14. **misscall-service-v12** - Missed call handling

### Integration Layers

- **auth-integration** - Auth service client & middleware
- **customer-integration** - Customer service client with DTOs
- **payment-integration** - Payment service client with DTOs
- **meal-integration** - Meal service client

### Infrastructure

- **gateway** - Kong API Gateway configuration
- **maps** - Location & mapping services
- **shared** - Shared utilities & components

## Usage Examples

### Check All Service Health

```bash
# List all services with health status
mcp-tool list_services

# Check specific service health
mcp-tool get_service_health --serviceName auth-service-v12
```

### Test API Endpoints

```bash
# Test customer registration
mcp-tool test_api_endpoint \
  --serviceName customer-service-v12 \
  --endpoint /customers \
  --method POST \
  --data '{"name":"John Doe","email":"<EMAIL>"}'

# Test order creation
mcp-tool test_api_endpoint \
  --serviceName quickserve-service-v12 \
  --endpoint /orders \
  --method POST \
  --data '{"customer_id":1,"items":[{"product_id":1,"quantity":2}]}'
```

### Database Operations

```bash
# Query customer data
mcp-tool query_database \
  --serviceName customer-service-v12 \
  --query "SELECT id, name, email FROM customers WHERE created_at > ?" \
  --params '["2024-01-01"]'

# Check order statistics
mcp-tool query_database \
  --serviceName quickserve-service-v12 \
  --query "SELECT status, COUNT(*) as count FROM orders GROUP BY status"
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: https://docs.onefooddialer.com
- Issues: GitHub Issues
