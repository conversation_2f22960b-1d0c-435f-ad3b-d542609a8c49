<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeliveryLocation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class DeliveryLocationController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer|max:99999',
                'per_page' => 'nullable|integer|min:1|max:100',
                'status' => 'nullable|integer|in:0,1',
                'search' => 'nullable|string',
                'city' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $companyId = (int) $request->input('company_id');
            $perPage = $request->input('per_page', 15);
            
            $query = DeliveryLocation::query()->byCompany($companyId);

            // Apply filters
            if ($request->has('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->has('search')) {
                $search = $request->input('search');
                $query->where('location', 'like', "%{$search}%");
            }

            if ($request->has('city')) {
                $query->byCity($request->input('city'));
            }
            
            $locations = $query->orderBy('location')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $locations->items(),
                'meta' => [
                    'current_page' => $locations->currentPage(),
                    'per_page' => $locations->perPage(),
                    'total' => $locations->total(),
                    'last_page' => $locations->lastPage(),
                    'company_id' => $companyId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get delivery locations', $e);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'location' => 'required|string|max:45',
                'city' => 'required|string|in:9',  // Only allow city ID 9 (Mumbai)
                'pin' => 'nullable|string|max:10',
                'sub_city_area' => 'nullable|string|max:45',
                'is_default' => 'nullable|in:0,1',
                'status' => 'required|integer|in:1',  // Only allow status 1 (active)
                'delivery_charges' => 'nullable|numeric|min:0',
                'delivery_time' => 'required|string|max:45',
                'fk_kitchen_code' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $data = $validator->validated();
            $data['company_id'] = config('company.id');
            $data['unit_id'] = config('company.unit_id');
            
            // Set defaults
            if (!isset($data['is_default'])) {
                $data['is_default'] = '0';
            } else {
                $data['is_default'] = (string) $data['is_default'];
            }
            
            if (!isset($data['fk_kitchen_code'])) {
                $data['fk_kitchen_code'] = null;
            }

            $location = DeliveryLocation::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Delivery location created successfully',
                'data' => $location,
                'meta' => [
                    'company_id' => config('company.id'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ], 201);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create delivery location', $e);
        }
    }

    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'company_id' => 'required|integer|max:99999',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $companyId = (int) $request->input('company_id');

            $location = DeliveryLocation::query()
                ->byCompany($companyId)
                ->where('pk_location_code', $id)
                ->first();

            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery location not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $location,
                'meta' => [
                    'company_id' => $companyId,
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get delivery location', $e, ['id' => $id]);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $location = DeliveryLocation::query()
                ->byCompany(config('company.id'))
                ->where('pk_location_code', $id)
                ->first();

            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery location not found',
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'location' => 'sometimes|required|string|max:45',
                'city' => 'sometimes|required|string|in:9',  // Only allow city ID 9 (Mumbai)
                'pin' => 'nullable|string|max:10',
                'sub_city_area' => 'nullable|string|max:45',
                'is_default' => 'nullable|in:0,1',
                'status' => 'sometimes|required|integer|in:1',  // Only allow status 1 (active)
                'delivery_charges' => 'nullable|numeric|min:0',
                'delivery_time' => 'sometimes|required|string|max:45',
                'fk_kitchen_code' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $data = $validator->validated();
            
            // Handle is_default field conversion
            if (isset($data['is_default'])) {
                $data['is_default'] = (string) $data['is_default'];
            }
            
            $location->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Delivery location updated successfully',
                'data' => $location->fresh(),
                'meta' => [
                    'company_id' => config('company.id'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update delivery location', $e, ['id' => $id]);
        }
    }

    public function destroy(int $id): JsonResponse
    {
        try {
            $location = DeliveryLocation::query()
                ->byCompany(config('company.id'))
                ->where('pk_location_code', $id)
                ->first();

            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery location not found',
                ], 404);
            }

            $location->delete();

            return response()->json([
                'success' => true,
                'message' => 'Delivery location deleted successfully',
                'meta' => [
                    'company_id' => config('company.id'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to delete delivery location', $e, ['id' => $id]);
        }
    }

    public function toggleStatus(int $id): JsonResponse
    {
        try {
            $location = DeliveryLocation::query()
                ->byCompany(config('company.id'))
                ->where('pk_location_code', $id)
                ->first();

            if (!$location) {
                return response()->json([
                    'success' => false,
                    'message' => 'Delivery location not found',
                ], 404);
            }

            $newStatus = $location->status === 1 ? 0 : 1;
            $location->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Delivery location status updated successfully',
                'data' => [
                    'id' => $location->pk_location_code,
                    'location' => $location->location,
                    'old_status' => $location->status === 1 ? 0 : 1,
                    'new_status' => $newStatus,
                    'is_active' => $newStatus === 1,
                ],
                'meta' => [
                    'company_id' => config('company.id'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to toggle delivery location status', $e, ['id' => $id]);
        }
    }

    public function stats(): JsonResponse
    {
        try {
            $companyId = config('company.id');

            $stats = [
                'total_locations' => DeliveryLocation::byCompany($companyId)->count(),
                'active_locations' => DeliveryLocation::byCompany($companyId)->active()->count(),
                'inactive_locations' => DeliveryLocation::byCompany($companyId)->where('status', 0)->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'meta' => [
                    'company_id' => $companyId,
                    'database' => config('company.database.name'),
                    'timestamp' => now()->toISOString(),
                    'api_version' => 'v2',
                ]
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get delivery locations statistics', $e);
        }
    }

    private function errorResponse(string $message, \Exception $e, array $context = []): JsonResponse
    {
        Log::error($message, array_merge([
            'error' => $e->getMessage(),
            'company_id' => config('company.id'),
        ], $context));

        return response()->json([
            'success' => false,
            'message' => $message,
            'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
        ], 500);
    }
}
