<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class WeeklyPlannerController extends Controller
{
    public function index(Request $request)
    {
        // validate required company_id and optional date params
        $validatedData = $request->validate([
            'company_id' => 'required|integer|min:1|max:99999',
            'from_date' => 'nullable|date|date_format:Y-m-d',
            'to_date' => 'nullable|date|date_format:Y-m-d|after_or_equal:from_date',
        ]);

        $companyId = (int) $request->input('company_id');

        // write a query for "SELECT * FROM live_quickserve_8163.products where product_type = 'Meal' and status =1;"
        $products = DB::table('products')
            ->where('company_id', $companyId)
            ->where('product_type', 'Meal')
            ->where('status', 1) // Active products only
            ->get(['pk_product_code', 'name', 'items', 'product_category']);
        
        
        if($products->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'No active meals found'
            ], 404);
        }

        // create week dates array for current week (Monday to Friday) 
        // if from_date and to_date is not given then take current weeks monday to friday dates else provide the dates from from_date to to_date
        $weekDates = [];
        $fromDate = $request->input('from_date', Carbon::now()->startOfWeek(Carbon::MONDAY)->format('Y-m-d'));
        $toDate = $request->input('to_date', Carbon::now()->endOfWeek(Carbon::FRIDAY)->format('Y-m-d'));
        $startDate = Carbon::parse($fromDate);
        $endDate = Carbon::parse($toDate);
        while ($startDate->lte($endDate)) {
            $weekDates[] = [
                'date' => $startDate->format('Y-m-d'),
                'day_name' => $startDate->format('l')
            ];
            $startDate->addDay();
        }

        // generate product category from products. Also if name has Jain then create separate category using above products array
        $mealCategories = [
            'breakfast' => [],
            'lunch' => [],
            'jain' => []
        ];
        foreach ($products as $product) {
            $name = strtolower($product->name);
            if (strpos($name, 'jain') === 0) {
                $mealCategories['jain'][] = $product;
            } elseif (strpos($name, 'breakfast') !== false) {
                $mealCategories['breakfast'][] = $product;
            } elseif (strpos($name, 'lunch') !== false) {
                $mealCategories['lunch'][] = $product;
            }
        }
        
        // get unique keys from items column of products table {"604":"1","605":"1","334":"1"}
        $mealPlanner = [];
        
        // APPROACH: Go category-wise first, then date-wise
        // For each category -> for each date -> for each product -> for each item
        foreach ($mealCategories as $category => $products) {
            $mealPlanner[$category] = [];
            
            foreach ($weekDates as $dateInfo) {
                $currentDate = $dateInfo['date'];
                $mealPlanner[$category][$currentDate] = [];
                
                foreach ($products as $product) {
                    if (!empty($product->items)) {
                        $items = json_decode($product->items, true);
                        if (is_array($items)) {
                            $mealData = [
                                'meal_id' => $product->pk_product_code,
                                'meal_name' => $product->name,
                                'date' => $currentDate,
                                'day_name' => $dateInfo['day_name'],
                                'items' => []
                            ];
                            
                            foreach ($items as $itemCode => $quantity) {
                                // Determine the correct menu type for planner query
                                // For Jain meals, use the actual meal type (breakfast/lunch) not "jain"
                                $menuType = $category;
                                if ($category === 'jain') {
                                    $productName = strtolower($product->name);
                                    if (strpos($productName, 'breakfast') !== false) {
                                        $menuType = 'breakfast';
                                    } elseif (strpos($productName, 'lunch') !== false) {
                                        $menuType = 'lunch';
                                    }
                                }
                                
                                // Check product_planner first for this specific date, meal_id, and item
                                $plannerItem = DB::table('product_planner')
                                    ->where('company_id', $companyId)
                                    ->where([
                                        'generic_product_code' => $itemCode,
                                        'meal_id' => $product->pk_product_code,
                                        'date' => $currentDate,
                                        'menu' => $menuType
                                    ])
                                    ->first(['specific_product_name']);
                                
                                // Skip items where planner name starts with "None"
                                if ($plannerItem && !empty($plannerItem->specific_product_name) && stripos($plannerItem->specific_product_name, 'None') === 0) {
                                    continue; // Skip this item completely
                                }
                                
                                $itemName = '';
                                $source = '';
                                
                                // Priority logic: planner data first, then fallback to products table
                                if ($plannerItem && !empty($plannerItem->specific_product_name)) {
                                    $itemName = $plannerItem->specific_product_name;
                                    $source = 'quickserve_planner_table';
                                } else {
                                    // Fallback to products table name
                                    $productDetails = DB::table('products')
                                        ->where('company_id', $companyId)
                                        ->where('pk_product_code', $itemCode)
                                        ->first(['name']);
                                    
                                    if ($productDetails) {
                                        $itemName = $productDetails->name;
                                        $source = 'quickserve_products_table_fallback';
                                    } else {
                                        $itemName = 'Unknown Item';
                                        $source = 'not_found';
                                    }
                                }
                                
                                $mealData['items'][] = [
                                    'item_code' => $itemCode,
                                    'item_name' => $itemName,
                                    'quantity' => $quantity,
                                    'source' => $source,
                                    'date' => $currentDate,
                                    'category' => $category
                                ];
                            }
                            
                            if (!empty($mealData['items'])) {
                                $mealPlanner[$category][$currentDate][] = $mealData;
                            }
                        }
                    }
                }
            }
        }
        // Return the organized meal planner data
        return response()->json([
            'success' => true,
            'message' => 'Weekly planner retrieved successfully',
            'data' => [
                'week_dates' => $weekDates,
                'meal_planner' => $mealPlanner,
                'summary' => [
                    'total_days' => count($weekDates),
                    'categories' => array_keys($mealCategories),
                    'date_range' => [
                        'from' => $weekDates[0]['date'] ?? null,
                        'to' => end($weekDates)['date'] ?? null
                    ]
                ]
            ]
        ]);
    }
}
