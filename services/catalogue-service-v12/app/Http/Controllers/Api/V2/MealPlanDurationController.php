<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MealPlanDurationController extends Controller
{
    /**
     * Get all available meal plans with duration information
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Validate required company context
            $request->validate([
                'company_id' => 'required|integer|min:1|max:99999',
            ]);
            $companyId = (int) $request->input('company_id');
            // Query plan_master table with promo code information
            $query = DB::table('plan_master')
                ->leftJoin('promo_codes', 'plan_master.fk_promo_code', '=', 'promo_codes.pk_promo_code')
                ->select([
                    'plan_master.pk_plan_code',
                    'plan_master.plan_name',
                    'plan_master.plan_quantity',
                    'plan_master.plan_period',
                    'plan_master.plan_type',
                    'plan_master.plan_start_date',
                    'plan_master.plan_end_date',
                    'plan_master.plan_status',
                    'plan_master.show_to_customer',
                    'plan_master.fk_kitchen_code',
                    'plan_master.fk_promo_code',
                    'promo_codes.promo_code',
                    'promo_codes.amount as discount_percentage',
                    'promo_codes.discount_type'
                ])
                ->where('plan_master.company_id', $companyId)
                ->where('plan_status', 1) // Only active plans
                ->where('show_to_customer', 'yes') // Only customer-visible plans
                ->whereDate('plan_start_date', '<=', now())
                ->whereDate('plan_end_date', '>=', now())
                ->orderBy('plan_quantity', 'asc');

            // Apply filters if provided
            if ($request->has('plan_type') && !empty($request->plan_type)) {
                $query->where('plan_type', $request->plan_type);
            }

            if ($request->has('plan_quantity') && !empty($request->plan_quantity)) {
                $query->where('plan_quantity', $request->plan_quantity);
            }

            $plans = $query->get();

            // Format the response to match business logic
            $companyIdForPricing = $companyId;
            $formattedPlans = $plans->map(function ($plan) use ($companyIdForPricing) {
                $pricing = $this->calculatePricing($companyIdForPricing, $plan->pk_plan_code, $plan->plan_quantity, $plan->discount_percentage, $plan->discount_type);
                
                return [
                    'id' => $plan->pk_plan_code,
                    'plan_name' => $plan->plan_name,
                    'plan_quantity' => $plan->plan_quantity,
                    'plan_period' => $plan->plan_period,
                    'plan_type' => $plan->plan_type,
                    'plan_start_date' => $plan->plan_start_date,
                    'plan_end_date' => $plan->plan_end_date,
                    'plan_status' => $plan->plan_status,
                    'show_to_customer' => $plan->show_to_customer,
                    'kitchen_id' => $plan->fk_kitchen_code,
                    'promo_code_id' => $plan->fk_promo_code,
                    'promo_code' => $plan->promo_code,
                    'discount_percentage' => $plan->discount_percentage,
                    'discount_type' => $plan->discount_type,
                    // Additional computed fields for UI
                    'duration_days' => $plan->plan_quantity,
                    'duration_description' => $this->getDurationDescription($plan->plan_name, $plan->plan_quantity),
                    'meal_count' => $this->getMealCount($plan->plan_name, $plan->plan_quantity),
                    // Pricing information
                    'price_per_meal' => $pricing['price_per_meal'],
                    'original_price' => $pricing['original_price'],
                    'discounted_price' => $pricing['discounted_price'],
                    'total_savings' => $pricing['total_savings'],
                    'currency' => 'INR',
                    'price_display' => $pricing['price_display'],
                    'product_count' => $pricing['product_count'],
                ];
            });

            return response()->json([
                'success' => true,
                'message' => 'Meal plans retrieved successfully',
                'data' => $formattedPlans,
                'meta' => [
                    'total' => $formattedPlans->count(),
                    'filtered_by' => $request->only(['plan_type', 'plan_quantity'])
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve meal plans',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific meal plan by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            // Validate required company context
            $request->validate([
                'company_id' => 'required|integer|min:1|max:99999',
            ]);
            $companyId = (int) $request->input('company_id');
            $plan = DB::table('plan_master')
                ->leftJoin('promo_codes', 'plan_master.fk_promo_code', '=', 'promo_codes.pk_promo_code')
                ->select([
                    'plan_master.pk_plan_code',
                    'plan_master.plan_name',
                    'plan_master.plan_quantity',
                    'plan_master.plan_period',
                    'plan_master.plan_type',
                    'plan_master.plan_start_date',
                    'plan_master.plan_end_date',
                    'plan_master.plan_status',
                    'plan_master.show_to_customer',
                    'plan_master.fk_kitchen_code',
                    'plan_master.fk_promo_code',
                    'promo_codes.promo_code',
                    'promo_codes.amount as discount_percentage',
                    'promo_codes.discount_type'
                ])
                ->where('plan_master.company_id', $companyId)
                ->where('plan_master.pk_plan_code', $id)
                ->where('plan_master.plan_status', 1)
                ->first();

            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Meal plan not found'
                ], 404);
            }

            $pricing = $this->calculatePricing($companyId, $plan->pk_plan_code, $plan->plan_quantity, $plan->discount_percentage, $plan->discount_type);
            
            $formattedPlan = [
                'id' => $plan->pk_plan_code,
                'plan_name' => $plan->plan_name,
                'plan_quantity' => $plan->plan_quantity,
                'plan_period' => $plan->plan_period,
                'plan_type' => $plan->plan_type,
                'plan_start_date' => $plan->plan_start_date,
                'plan_end_date' => $plan->plan_end_date,
                'plan_status' => $plan->plan_status,
                'show_to_customer' => $plan->show_to_customer,
                'kitchen_id' => $plan->fk_kitchen_code,
                'promo_code_id' => $plan->fk_promo_code,
                'promo_code' => $plan->promo_code,
                'discount_percentage' => $plan->discount_percentage,
                'discount_type' => $plan->discount_type,
                // Additional computed fields for UI
                'duration_days' => $plan->plan_quantity,
                'duration_description' => $this->getDurationDescription($plan->plan_name, $plan->plan_quantity),
                'meal_count' => $this->getMealCount($plan->plan_name, $plan->plan_quantity),
                // Pricing information
                'price_per_meal' => $pricing['price_per_meal'],
                'original_price' => $pricing['original_price'],
                'discounted_price' => $pricing['discounted_price'],
                'total_savings' => $pricing['total_savings'],
                'currency' => 'INR',
                'price_display' => $pricing['price_display'],
                'product_count' => $pricing['product_count'],
            ];

            return response()->json([
                'success' => true,
                'message' => 'Meal plan retrieved successfully',
                'data' => $formattedPlan
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve meal plan',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get duration description based on plan name and quantity
     */
    private function getDurationDescription(string $planName, int $planQuantity): string
    {
        $lowerPlanName = strtolower($planName);
        
        // Extract duration from plan name first
        if (str_contains($lowerPlanName, 'single')) {
            return 'Single Day';
        }
        
        if (str_contains($lowerPlanName, 'week')) {
            return 'Weekly';
        }
        
        if (str_contains($lowerPlanName, 'month')) {
            return 'Monthly';
        }
        
        if (str_contains($lowerPlanName, 'quarter')) {
            return 'Quarterly';
        }
        
        if (str_contains($lowerPlanName, 'annual')) {
            return 'Annual';
        }

        // Fallback based on quantity
        if ($planQuantity <= 1) {
            return 'Single Day';
        }
        
        if ($planQuantity <= 7) {
            return 'Weekly';
        }
        
        if ($planQuantity <= 31) {
            return 'Monthly';
        }
        
        if ($planQuantity <= 90) {
            return 'Quarterly';
        }
        
        if ($planQuantity <= 180) {
            return 'Half-Yearly';
        }
        
        return 'Annual';
    }

    /**
     * Get meal count based on plan name and quantity
     */
    private function getMealCount(string $planName, int $planQuantity): int
    {
        // For plans that specify meal count in the name
        if (preg_match('/(\d+)\s*(meal|day)/', strtolower($planName), $matches)) {
            return (int) $matches[1];
        }
        
        // Default to plan quantity
        return $planQuantity;
    }

    /**
     * Calculate pricing based on real product data and promo codes
     */
    private function calculatePricing(int $companyId, int $planId, ?int $planQuantity, ?float $discountPercentage, ?string $discountType): array
    {
        // Get products associated with this plan
        $products = DB::table('products')
            ->select(['pk_product_code', 'name', 'unit_price', 'category', 'meal_plans'])
            ->where('company_id', $companyId)
            ->where('meal_plans', 'LIKE', "%{$planId}@%")
            ->where('status', 1)
            ->get();

        // Calculate average price per meal from all products in this plan
        $totalPrice = 0.0;
        $productCount = 0;
        
        foreach ($products as $product) {
            $totalPrice += $product->unit_price;
            $productCount++;
        }
        
        // Default base price if no products found (fallback)
        $basePricePerMeal = $productCount > 0 ? ($totalPrice / $productCount) : 100.0;
        
        // Calculate original price for the entire plan
        $originalPrice = $basePricePerMeal * ($planQuantity ?? 1);
        
        // Apply discount if available
        $discountAmount = 0.0;
        if ($discountPercentage && $discountType === 'percentage') {
            $discountAmount = ($originalPrice * $discountPercentage) / 100;
        }
        
        $discountedPrice = $originalPrice - $discountAmount;
        
        return [
            'price_per_meal' => round($basePricePerMeal, 2),
            'original_price' => round($originalPrice, 2),
            'discounted_price' => round($discountedPrice, 2),
            'total_savings' => round($discountAmount, 2),
            'price_display' => $this->formatPriceDisplay($originalPrice, $discountedPrice, $discountPercentage),
            'product_count' => $productCount
        ];
    }

    /**
     * Format price display for UI to match screenshot
     */
    private function formatPriceDisplay(float $originalPrice, float $discountedPrice, ?float $discountPercentage): string
    {
        if ($discountPercentage && $discountPercentage > 0) {
            // Format: "₹1,500 ₹1,350" (strikethrough original price, discounted price)
            return "₹" . number_format($originalPrice, 0) . " ₹" . number_format($discountedPrice, 0);
        }
        
        return "₹" . number_format($discountedPrice, 0);
    }
}
