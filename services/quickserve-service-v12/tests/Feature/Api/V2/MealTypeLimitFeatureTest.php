<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V2;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class MealTypeLimitFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
        // Minimal seed: create two lunch products and two breakfast products
        DB::table('products')->insert([
            ['pk_product_code' => 101, 'name' => 'Veg Lunch', 'description' => 'Lunch', 'product_type' => 'Meal', 'unit_price' => 100, 'status' => 1],
            ['pk_product_code' => 102, 'name' => 'NonVeg Lunch', 'description' => 'Lunch', 'product_type' => 'Meal', 'unit_price' => 120, 'status' => 1],
            ['pk_product_code' => 201, 'name' => 'Veg Breakfast', 'description' => 'Breakfast', 'product_type' => 'Meal', 'unit_price' => 60, 'status' => 1],
            ['pk_product_code' => 202, 'name' => 'NonVeg Breakfast', 'description' => 'Breakfast', 'product_type' => 'Meal', 'unit_price' => 70, 'status' => 1],
        ]);
    }

    private function basePayload(): array
    {
        return [
            'customer_id' => 1,
            'customer_address' => 'Addr',
            'location_code' => 1,
            'location_name' => 'Loc',
            'city' => 1,
            'city_name' => 'City',
            'company_id' => 8163,
            'unit_id' => 1,
            'fk_kitchen_code' => 1,
            'meals' => [],
            'start_date' => now()->toDateString(),
            'selected_days' => [now()->dayOfWeekIso % 7],
            'subscription_days' => 1,
        ];
    }

    public function test_two_lunch_products_in_single_day_rejected(): void
    {
        $payload = $this->basePayload();
        $payload['meals'] = [
            ['product_code' => 101, 'quantity' => 1],
            ['product_code' => 102, 'quantity' => 1],
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(422);
        $res->assertJson(['success' => false, 'message' => 'Only one menu item can be added per meal type for this day', 'meal_type' => 'lunch']);
    }

    public function test_two_breakfast_quantity_in_single_day_rejected(): void
    {
        $payload = $this->basePayload();
        $payload['meals'] = [
            ['product_code' => 201, 'quantity' => 2],
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(422);
        $res->assertJson(['success' => false, 'message' => 'Only one menu item can be added per meal type for this day', 'meal_type' => 'breakfast']);
    }

    public function test_one_lunch_and_one_breakfast_allowed(): void
    {
        $payload = $this->basePayload();
        $payload['meals'] = [
            ['product_code' => 101, 'quantity' => 1], // lunch
            ['product_code' => 201, 'quantity' => 1], // breakfast
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(200);
    }
}
