<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V2;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class PerDateOrderFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
        DB::table('products')->insert([
            ['pk_product_code' => 101, 'name' => 'Veg Lunch', 'description' => 'Lunch', 'product_type' => 'Meal', 'unit_price' => 100, 'status' => 1],
            ['pk_product_code' => 201, 'name' => 'Veg Breakfast', 'description' => 'Breakfast', 'product_type' => 'Meal', 'unit_price' => 60, 'status' => 1],
            ['pk_product_code' => 202, 'name' => 'NonVeg Breakfast', 'description' => 'Breakfast', 'product_type' => 'Meal', 'unit_price' => 70, 'status' => 1],
        ]);
    }

    private function basePayload(): array
    {
        return [
            'customer_id' => 1,
            'customer_address' => 'Addr',
            'location_code' => 1,
            'location_name' => 'Loc',
            'city' => 1,
            'city_name' => 'City',
            'company_id' => 8163,
            'unit_id' => 1,
            'fk_kitchen_code' => 1,
        ];
    }

    public function test_per_date_different_meals_on_different_dates_allowed(): void
    {
        $today = now()->toDateString();
        $tomorrow = now()->addDay()->toDateString();
        $payload = $this->basePayload();
        $payload['meals_by_date'] = [
            ['date' => $today, 'meals' => [ ['product_code' => 101, 'quantity' => 1] ]], // lunch today
            ['date' => $tomorrow, 'meals' => [ ['product_code' => 201, 'quantity' => 1] ]], // breakfast tomorrow
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(201);
        $res->assertJson(['success' => true]);
    }

    public function test_per_date_two_breakfasts_same_date_rejected(): void
    {
        $today = now()->toDateString();
        $payload = $this->basePayload();
        $payload['meals_by_date'] = [
            ['date' => $today, 'meals' => [ ['product_code' => 201, 'quantity' => 1], ['product_code' => 202, 'quantity' => 1] ]],
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(422);
        $res->assertJson(['success' => false, 'message' => 'Only one menu item can be added per meal type for this day', 'meal_type' => 'breakfast', 'date' => $today]);
    }
}
