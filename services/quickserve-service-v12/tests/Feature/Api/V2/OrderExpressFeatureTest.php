<?php

declare(strict_types=1);

namespace Tests\Feature\Api\V2;

use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class OrderExpressFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    private function seedSetting(string $key, string $value, ?int $companyId = 8163): void
    {
        $data = [];
        if (Schema::hasColumn('settings', 'company_id')) {
            $data['company_id'] = $companyId;
        }
        if (Schema::hasColumn('settings', 'key')) {
            $data['key'] = $key;
        }
        if (Schema::hasColumn('settings', 'value')) {
            $data['value'] = $value;
        }
        if (Schema::hasColumn('settings', 'setting_key')) {
            $data['setting_key'] = $key;
        }
        if (Schema::hasColumn('settings', 'setting_value')) {
            $data['setting_value'] = $value;
        }
        DB::table('settings')->insert($data);
    }

    public function test_create_order_applies_express_extra_delivery_charge_during_extended_window(): void
    {
        // Now at 18:26 so extended window (till 19:00) is active
        Carbon::setTestNow(Carbon::create(2025, 8, 22, 18, 26, 0));
        $this->seedSetting('EXPRESS_EXTENDED_ENABLED', 'yes');
        $this->seedSetting('EXPRESS_EXTENDED_END_TIME', '19:00:00');
        // Lunch cutoff set to midnight to trigger extended express window
        $this->seedSetting('K1_LUNCH_ORDER_CUT_OFF_TIME', '00:00:00');
        // Charge configured
        $this->seedSetting('K1_LUNCH_EXPRESS_EXTRA_DELIVERY_CHARGE', '40');

        $payload = [
            'user_id' => 999,
            'company_id' => 8163,
            'unit_id' => 1,
            'fk_kitchen_code' => '1',
            'customer_name' => 'Test User',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9999999999',
            'is_express' => true,
            'meals' => [
                [
                    'meal_type' => 'lunch',
                    'items' => [
                        [
                            'product_code' => 'P001',
                            'product_name' => 'Thali',
                            'quantity' => 1,
                            'amount' => 100.0,
                        ],
                    ],
                ],
            ],
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(200);
        $json = $res->json();

        $this->assertArrayHasKey('meals', $json);
        $this->assertSame('lunch', $json['meals'][0]['meal_type']);
        $this->assertEquals(40.0, (float)($json['meals'][0]['delivery_charge'] ?? 0.0), 'Lunch delivery charge should include express extra 40');
    }

    public function test_create_order_after_extended_window_has_no_express_charge(): void
    {
        // Time after 19:00
        Carbon::setTestNow(Carbon::create(2025, 8, 22, 19, 10, 0));
        $this->seedSetting('EXPRESS_EXTENDED_ENABLED', 'yes');
        $this->seedSetting('EXPRESS_EXTENDED_END_TIME', '19:00:00');
        $this->seedSetting('K1_LUNCH_ORDER_CUT_OFF_TIME', '00:00:00');
        $this->seedSetting('K1_LUNCH_EXPRESS_EXTRA_DELIVERY_CHARGE', '40');

        $payload = [
            'user_id' => 999,
            'company_id' => 8163,
            'unit_id' => 1,
            'fk_kitchen_code' => '1',
            'customer_name' => 'Test User',
            'customer_email' => '<EMAIL>',
            'customer_phone' => '9999999999',
            'is_express' => true,
            'meals' => [
                [
                    'meal_type' => 'lunch',
                    'items' => [
                        [
                            'product_code' => 'P001',
                            'product_name' => 'Thali',
                            'quantity' => 1,
                            'amount' => 100.0,
                        ],
                    ],
                ],
            ],
        ];

        $res = $this->postJson('/api/v2/order-management/create', $payload);
        $res->assertStatus(200);
        $json = $res->json();
        $this->assertEquals(0.0, (float)($json['meals'][0]['delivery_charge'] ?? 0.0), 'No express charge should apply after extended window');
    }
}
