<?php

declare(strict_types=1);

namespace Tests\Unit;

use App\Http\Controllers\Api\V2\OrderManagementController;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Tests\TestCase;

class OrderExpressLogicTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // Ensure migrations run for sqlite memory
        $this->artisan('migrate');
    }

    private function seedSetting(string $key, string $value, ?int $companyId = 8163): void
    {
        $data = [];
        if (Schema::hasColumn('settings', 'company_id')) {
            $data['company_id'] = $companyId;
        }
        if (Schema::hasColumn('settings', 'key')) {
            $data['key'] = $key;
        }
        if (Schema::hasColumn('settings', 'value')) {
            $data['value'] = $value;
        }
        if (Schema::hasColumn('settings', 'setting_key')) {
            $data['setting_key'] = $key;
        }
        if (Schema::hasColumn('settings', 'setting_value')) {
            $data['setting_value'] = $value;
        }
        DB::table('settings')->insert($data);
    }

    private function callIsWithinExpressWindow(OrderManagementController $ctrl, string $cutoffTime, ?int $cutoffDay, string $mealType, ?int $companyId): bool
    {
        $ref = new \ReflectionMethod($ctrl, 'isWithinExpressWindow');
        $ref->setAccessible(true);
        return (bool) $ref->invoke($ctrl, $cutoffTime, $cutoffDay, $mealType, $companyId);
    }

    private function callGetExpressExtraDeliveryCharge(OrderManagementController $ctrl, int $kitchenId, string $mealType, ?int $companyId): float
    {
        $ref = new \ReflectionMethod($ctrl, 'getExpressExtraDeliveryCharge');
        $ref->setAccessible(true);
        return (float) $ref->invoke($ctrl, $kitchenId, $mealType, $companyId);
    }

    public function test_extended_midnight_window_allows_until_configured_end_time(): void
    {
        Carbon::setTestNow(Carbon::create(2025, 8, 22, 18, 26, 0)); // 18:26
        $this->seedSetting('EXPRESS_EXTENDED_ENABLED', 'yes');
        $this->seedSetting('EXPRESS_EXTENDED_END_TIME', '19:00:00');
        $ctrl = app(OrderManagementController::class);

        $within = $this->callIsWithinExpressWindow($ctrl, '00:00:00', null, 'lunch', 8163);
        $this->assertTrue($within, 'Expected express window to be open until 19:00 when cutoff is midnight');

        $charge = $this->callGetExpressExtraDeliveryCharge($ctrl, 1, 'lunch', 8163);
        $this->assertSame(0.0, $charge, 'Default extra charge should be 0.0 when not set');

        $this->seedSetting('K1_LUNCH_EXPRESS_EXTRA_DELIVERY_CHARGE', '40');
        $charge2 = $this->callGetExpressExtraDeliveryCharge($ctrl, 1, 'lunch', 8163);
        $this->assertSame(40.0, $charge2);
    }

    public function test_extended_window_closes_after_end_time(): void
    {
        Carbon::setTestNow(Carbon::create(2025, 8, 22, 19, 1, 0)); // 19:01
        $this->seedSetting('EXPRESS_EXTENDED_ENABLED', 'yes');
        $this->seedSetting('EXPRESS_EXTENDED_END_TIME', '19:00:00');
        $ctrl = app(OrderManagementController::class);

        $within = $this->callIsWithinExpressWindow($ctrl, '00:00:00', null, 'breakfast', 8163);
        $this->assertFalse($within, 'Express window should be closed after 19:00:00');
    }

    public function test_normal_cutoff_window_respected(): void
    {
        $ctrl = app(OrderManagementController::class);

        Carbon::setTestNow(Carbon::create(2025, 8, 22, 7, 30, 0));
        $this->assertTrue($this->callIsWithinExpressWindow($ctrl, '08:00:00', null, 'lunch', 8163));

        Carbon::setTestNow(Carbon::create(2025, 8, 22, 8, 30, 0));
        $this->assertFalse($this->callIsWithinExpressWindow($ctrl, '08:00:00', null, 'lunch', 8163));
    }
}
