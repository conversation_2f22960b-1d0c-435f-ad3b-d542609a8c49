<?php

namespace App\Http\Requests\Api\V2;

use Illuminate\Foundation\Http\FormRequest;

class GetCustomerOrdersRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Filters (all optional)
            'student_name_filter' => ['nullable', 'string', 'max:250'],
            // Accept common boolean strings too; controller will cast via $request->boolean()
            'include_cancelled' => ['nullable', 'in:1,0,true,false,on,off'],
            'order_status' => ['nullable', 'string', 'max:50'],

            // Date range
            'start_date' => ['nullable', 'date_format:Y-m-d'],
            'end_date' => ['nullable', 'date_format:Y-m-d', 'after_or_equal:start_date'],

            // Pagination controls
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'page' => ['nullable', 'integer', 'min:1'],
        ];
    }

    public function messages(): array
    {
        return [
            'end_date.after_or_equal' => 'The end_date must be a date after or equal to start_date.',
        ];
    }
}
